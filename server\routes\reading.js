const express = require('express');
const router = express.Router();
const axios = require('axios');
const { User } = require('../models/User');
const { Spread } = require('../models/tarot');
const { authenticateToken, optionalAuthenticateToken } = require('../middleware/auth');
const { TAROT_CARDS } = require('../constants/tarot');
const { getConnection } = require('../services/database');
const readers = require('../config/readers');
const { zhipuAPI, DoubaoAPI } = require('../services/apiClients');
// const { checkEthicalIssues } = require('../services/ethicsCheck'); // 已禁用安全检测
const OpenAI = require('openai');

// 添加多语言支持的reader prompts
const readerPromptsMultiLang = {
  'zh-CN': readers, // 默认中文
};

// 尝试加载其他语言配置文件
try {
  readerPromptsMultiLang['zh-TW'] = require('../config/readers_zh_tw');
  // console.log('成功加载繁体中文配置文件');
} catch (error) {
  // console.log('未找到繁体中文配置文件，将使用默认中文配置');
}

try {
  readerPromptsMultiLang['en'] = require('../config/readers_en');
  // console.log('成功加载英文配置文件');
} catch (error) {
  // console.log('未找到英文配置文件，将使用默认中文配置');
}

try {
  readerPromptsMultiLang['ja'] = require('../config/readers_ja');
  // console.log('成功加载日文配置文件');
} catch (error) {
  // console.log('未找到日文配置文件，将使用默认中文配置');
}

// 根据语言获取对应的reader prompt
function getReaderPromptByLanguage(readerId, language = 'zh-CN') {
  // 标准化language参数
  let normalizedLang = language;
  if (language.startsWith('en-')) normalizedLang = 'en';
  if (language.startsWith('ja-')) normalizedLang = 'ja';
  if (language.startsWith('zh-TW')) normalizedLang = 'zh-TW';
  if (language.startsWith('zh-CN') || (language.startsWith('zh-') && !language.startsWith('zh-TW'))) normalizedLang = 'zh-CN';
  
  // 尝试按优先级依次获取reader prompt
  const langPrompts = readerPromptsMultiLang[normalizedLang] || readers;
  
  if (langPrompts) {
    // 如果找到对应语言的readers配置
    const reader = langPrompts.find(r => r.id.toLowerCase() === readerId.toLowerCase());
    if (reader) {
      return reader.prompt;
    }
  }
  
  // 如果没找到对应语言的reader，尝试使用默认中文
  // 这确保了即使没有对应语言版本的配置文件，系统也能正常工作
  const defaultReader = readers.find(r => r.id.toLowerCase() === readerId.toLowerCase());
  return defaultReader?.prompt || readers[0].prompt;
}

// 添加会话锁，防止重复处理相同sessionId的请求
const activeSessions = new Map();

// 创建OpenAI实例用于调用Qwen API
const qwenAPI = new OpenAI({
  apiKey: process.env.QWEN_API_KEY || "YOUR_API_KEY_HERE",
  baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  // 关闭API调试日志
  logLevel: 'off',
  // 设置debug为false强制关闭日志
  debug: false,
  // 设置默认API请求配置
  defaultQuery: { debug: false },
  defaultHeaders: { 'Debug-Logs': 'false' }
});

// 设置环境变量以禁用OpenAI日志
process.env.DEBUG = "false";
process.env.OPENAI_LOG = "off";

// // 创建用于调用 Deepseek API 的 axios 实例
// const deepseekAPI = axios.create({
//   baseURL: 'https://api.deepseek.com',
//   timeout: 500000,
//   headers: {
//     'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
//     'Content-Type': 'application/json'
//   }
// });

// 检查用户是否重复提问同一问题的函数
async function checkRepeatedQuestion(userId, question) {
  try {
    const pool = await getConnection();
    // 获取用户最近7天内的会话
    const [recentSessions] = await pool.query(`
      SELECT 
        question, 
        DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i') as reading_time, 
        timestamp,
        DATEDIFF(NOW(), timestamp) as days_diff  
      FROM sessions 
      WHERE user_id = ? 
      AND question IS NOT NULL 
      AND status = 'completed'
      AND timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      ORDER BY timestamp DESC 
      LIMIT 3
    `, [userId]);

    // 如果没有历史会话，直接返回不是重复提问
    if (recentSessions.length === 0) {
      return { isRepeated: false };
    }

    // 首先进行简单字符串匹配检测（完全匹配优先）
    for (const session of recentSessions) {
      if (session.question.toLowerCase().trim() === question.toLowerCase().trim()) {
        return { 
          isRepeated: true, 
          matchedQuestion: session.question,
          confidence: 1.0,
          timeDifference: session.days_diff || 0
        };
      }
    }

    // 使用GLM-4-Air判断问题是否相似
    const systemPrompt = `你是一个专业的塔罗牌问题相似度分析助手，你需要判断用户的新问题与历史问题是否本质上是寻求同一答案的相同问题。请分析问题的核心意图而非表达方式。
你必须直接返回JSON格式数据，格式如下：
{
  "isRepeated": true/false,
  "matchedQuestion": "匹配到的问题（如果有）"
}
如果新问题与任一历史问题是同一个问题，将isRepeated设为true，matchedQuestion填写匹配到的历史问题。
不要包含任何其他代码、代码块标记或解释。`;

    const userPrompt = `
新问题: ${question}
历史问题:
${recentSessions.map(s => `- ${s.reading_time}: ${s.question}`).join('\n')}
`;

    console.log('\n正在判断问题是否重复...');
    const response = await zhipuAPI.post('chat/completions', {
      model: "GLM-4-Flash-250414",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ]
    });

    if (!response.data?.choices?.[0]?.message?.content) {
      console.error('AI API响应格式错误:', response.data);
      return { isRepeated: false };
    }

    const content = response.data.choices[0].message.content;
    console.log('\nAI相似度分析响应:', content);
    
    // 记录输入和输出token数量
    const inputTokens = response.data.usage?.prompt_tokens || 0;
    const outputTokens = response.data.usage?.completion_tokens || 0;
    // console.log(`相似度检测API使用: 输入tokens: ${inputTokens}, 输出tokens: ${outputTokens}`);

    try {
      // 清理各种可能的代码块标记和注释
      let cleanContent = content.replace(/```json\s*|\s*```|```python\s*|\s*```|```\s*|\s*```/g, '').trim();
      
      // 尝试提取JSON对象
      const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanContent = jsonMatch[0];
      }
      
      // 移除JSON中的注释
      cleanContent = cleanContent.replace(/\/\/.*$/gm, '').trim();
      
      // console.log('处理后的JSON内容:', cleanContent);
      
      const result = JSON.parse(cleanContent);
      
      return {
        isRepeated: result.isRepeated || false,
        matchedQuestion: result.matchedQuestion || '',
        confidence: result.confidence || 0,
        timeDifference: result.timeDifference || 0,
        input_tokens: inputTokens,
        output_tokens: outputTokens
      };
    } catch (parseError) {
      console.error('解析 AI 响应失败:', parseError);
      console.error('解析失败的内容:', content);
      
      // 如果解析失败，进行简单的字符串相似度检测
      console.log('尝试进行简单相似度检测重复问题...');
      
      const userQuestion = question.toLowerCase().trim();
      let highestSimilarity = 0;
      let matchedSession = null;
      
      for (const session of recentSessions) {
        const sessionQuestion = session.question.toLowerCase().trim();
        
        // 计算简单相似度（共同单词数量）
        const userWords = new Set(userQuestion.split(/\s+/));
        const sessionWords = new Set(sessionQuestion.split(/\s+/));
        
        // 计算交集大小
        let intersection = 0;
        for (const word of userWords) {
          if (sessionWords.has(word)) {
            intersection++;
          }
        }
        
        // 计算相似度
        const similarity = intersection / Math.max(userWords.size, sessionWords.size);
        
        if (similarity > highestSimilarity && similarity > 0.7) {
          highestSimilarity = similarity;
          matchedSession = session;
        }
      }
      
      if (matchedSession) {
        return { 
          isRepeated: true, 
          matchedQuestion: matchedSession.question,
          confidence: highestSimilarity,
          timeDifference: matchedSession.days_diff || 0,
          input_tokens: inputTokens,
          output_tokens: outputTokens
        };
      }
      
      return { isRepeated: false, input_tokens: inputTokens, output_tokens: outputTokens };
    }
  } catch (error) {
    console.error('检查重复问题失败:', error);
    return { isRepeated: false, input_tokens: 0, output_tokens: 0 };
  }
}

// 根据语言类型获取用户提问prompt
function getUserPromptByLanguage(language, userInfo, question, selectedSpread, cardsDescription, historyText, currentFullDate) {
  // 标准化language参数
  let normalizedLang = language;
  if (language.startsWith('en-')) normalizedLang = 'en';
  if (language.startsWith('ja-')) normalizedLang = 'ja';
  if (language.startsWith('zh-TW')) normalizedLang = 'zh-TW';
  if (language.startsWith('zh-CN'))  normalizedLang = 'zh-CN';

  // 获取翻译后的牌阵名称
  const getTranslatedSpreadName = (spreadId) => {
    // 将id中的连字符替换为下划线，以匹配i18n文件中的格式
    const normalizedId = spreadId.replace(/-/g, '_');
    
    try {
      // 尝试从i18n文件中获取翻译
      const i18nFile = require(`../i18n/locales/${normalizedLang}.json`);
      if (i18nFile.spreads && i18nFile.spreads[normalizedId] && 
          i18nFile.spreads[normalizedId].name) {
        return i18nFile.spreads[normalizedId].name;
      }
    } catch (error) {
      console.log(`未找到${normalizedLang}语言的牌阵名称翻译，使用默认值`);
    }
    
    // 如果没有找到翻译，返回原始值
    return selectedSpread.name || '标准塔罗牌阵';
  };

  // 获取翻译后的牌阵名称
  const spreadName = getTranslatedSpreadName(selectedSpread.id);

  // 是否牌阵特殊提示
  const yesNoHint = {
    'zh-CN': `注意：请根据以下规则，综合分析三张牌的情况，给出最终的是/否答案：
1. 三张牌均为"是"：表示肯定答案
2. 两张"是"牌 + 一张"否/中性"牌：表示可能性较大，但需要时间
3. 一张"是"牌 + 两张"否/中性"牌：表示可能性较小，需要付出更多努力
4. 三张牌均为"否"或"否"与"中性"混合：表示否定答案
请在解读中明确指出最终结论。`,
    'zh-TW': `注意：請根據以下規則，綜合分析三張牌的情況，給出最終的是/否答案：
1. 三張牌均為"是"：表示肯定答案
2. 兩張"是"牌 + 一張"否/中性"牌：表示可能性較大，但需要時間
3. 一張"是"牌 + 兩張"否/中性"牌：表示可能性較小，需要付出更多努力
4. 三張牌均為"否"或"否"與"中性"混合：表示否定答案
請在解讀中明確指出最終結論。`,
    'en': `Note: Please follow these rules to analyze the three cards and provide a final Yes/No answer:
1. All three cards indicate "Yes": This represents a definite affirmative answer
2. Two "Yes" cards + one "No/Neutral" card: This indicates a high probability, but requires time
3. One "Yes" card + two "No/Neutral" cards: This suggests a low probability, requiring more effort
4. All three cards indicate "No" or a mix of "No" and "Neutral": This represents a negative answer
Please clearly state your final conclusion in the interpretation.`,
    'ja': `注意：以下のルールに従って、3枚のカードを総合的に分析し、最終的なはい/いいえの答えを出してください：
1. 3枚全てが「はい」を示す場合：肯定的な答え
2. 2枚の「はい」カード + 1枚の「いいえ/中立」カード：可能性は高いが、時間が必要
3. 1枚の「はい」カード + 2枚の「いいえ/中立」カード：可能性は低く、より多くの努力が必要
4. 3枚全てが「いいえ」または「いいえ」と「中立」の混合：否定的な答え
解釈の中で最終的な結論を明確に述べてください。`
  };

  // 不同语言的提示模板
  const templates = {
    'zh-CN': `
请使用简体中文回答
每张卡牌解读文字保证在100-150字
不要使用任何markdown格式
请为问卜者答疑解惑，拨开迷雾，指引前路
今日日期：${currentFullDate}

问卜者信息：
姓名：${userInfo.username}
性别：${userInfo.gender || '未知'}
地区：${userInfo.location || '未知'}
生日：${userInfo.birthday || '未知'}
历史问卜信息：${historyText}
问题：${question}

塔罗牌阵：${spreadName}
${selectedSpread.id === 'yes-no' ? yesNoHint['zh-CN'] : ''}
牌阵解读：
${cardsDescription}
`,
    'zh-TW': `
請使用繁體中文回答
每張卡牌解讀文字保證在100-150字
不要使用任何markdown格式
請為問卜者答疑解惑，撥開迷霧，指引前路
今日日期：${currentFullDate}

問卜者資訊：
姓名：${userInfo.username}
性別：${userInfo.gender || '未知'}
地區：${userInfo.location || '未知'}
生日：${userInfo.birthday || '未知'}
歷史問卜資訊：${historyText}
問題：${question}

塔羅牌陣：${spreadName}
${selectedSpread.id === 'yes-no' ? yesNoHint['zh-TW'] : ''}
牌陣解讀：
${cardsDescription}
`,
    'en': `
Please answer in English
Ensure each card interpretation is between 100-150 words
Do not use any markdown formatting
Please provide clarity and guidance for the querent
Today's date: ${currentFullDate}

Querent information:
Name: ${userInfo.username}
Gender: ${userInfo.gender || 'Unknown'}
Location: ${userInfo.location || 'Unknown'}
Birthday: ${userInfo.birthday || 'Unknown'}
Past reading history: ${historyText}
Question: ${question}

Tarot spread: ${spreadName}
${selectedSpread.id === 'yes-no' ? yesNoHint['en'] : ''}
Card interpretations:
${cardsDescription}
`,
    'ja': `
日本語で答えてください
各カードの解釈は100～150文字程度にしてください
マークダウン形式は使用しないでください
問い手の悩みを解消し、霧を晴らし、道を指し示してください
今日の日付：${currentFullDate}

問い手の情報：
名前：${userInfo.username}
性別：${userInfo.gender || '不明'}
地域：${userInfo.location || '不明'}
誕生日：${userInfo.birthday || '不明'}
過去のリーディング履歴：${historyText}
質問：${question}

タロットスプレッド：${spreadName}
${selectedSpread.id === 'yes-no' ? yesNoHint['ja'] : ''}
カード解釈：
${cardsDescription}
`
  };

  // 返回对应语言的模板，如果没有对应语言则返回繁体中文版本（默认）
  return templates[normalizedLang] || templates['zh-TW'];
}

router.post('/', optionalAuthenticateToken, async (req, res) => {
  try {
    const {
      question,
      selectedCards,
      selectedSpread,
      sessionId,
      language,
      fingerprint // 添加指纹参数用于匿名用户
    } = req.body;

    // 处理匿名用户和登录用户
    if (!req.user) {
      // 匿名用户逻辑
      if (!fingerprint) {
        return res.status(400).json({
          error: '匿名用户需要提供浏览器指纹'
        });
      }

      // 检查匿名用户是否已使用过免费机会
      const pool = await getConnection();
      const [existingRecords] = await pool.query(
        'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
        [fingerprint]
      );

      if (existingRecords[0].count > 0) {
        return res.status(403).json({
          error: '您的免费占卜次数已用完，请登录获取更多次数',
          errorCode: 'ANONYMOUS_LIMIT_EXCEEDED'
        });
      }

      console.log('🔍 [Reading API] 匿名用户请求，指纹:', fingerprint);
    }

    const userId = req.user ? req.user.userId : null;

    // 检查会话锁，如果该会话已经在处理中或者已完成，则不再处理
    if (activeSessions.has(sessionId)) {
      console.log(`会话 ${sessionId} 正在处理中或已处理完成，跳过重复请求`);
      return res.status(409).json({ 
        error: '该会话已经在处理中或已经处理完成', 
        code: 'SESSION_ALREADY_IN_PROGRESS'
      });
    }
    
    // 标记该会话为正在处理
    activeSessions.set(sessionId, {
      status: 'processing',
      startTime: Date.now()
    });

    let user = null;
    let userDetails = [];

    // 只有登录用户才查询用户信息
    if (userId) {
      user = await User.findById(userId);

      // 从数据库获取用户详细信息
      const pool = await getConnection();
      const [details] = await pool.query(
        'SELECT gender, location, birthday, user_profile FROM users WHERE id = ?',
        [userId]
      );
      userDetails = details;
    } else {
      console.log('🔍 [Reading API] 匿名用户，跳过用户信息查询');
    }

    // 获取当前年月
    const now = new Date();
    const currentDate = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月`;
    const currentFullDate = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日`;

    if (!question || !selectedCards || !selectedSpread || !sessionId) {
      return res.status(400).json({ error: '缺少必要的参数' });
    }

    // 获取客户端请求的语言，优先使用请求参数，其次用户配置，最后默认繁体中文
    const clientLanguage = language || user.language || 'zh-TW';

    // 检查问题是否涉及伦理问题 - 已禁用安全检测
    // let questionCheck;
    // const ethicsCheck = await checkEthicalIssues(question, clientLanguage);

    // 模拟安全检测结果，直接返回无安全问题
    const ethicsCheck = {
      whetherOrNot: false,
      category: '无安全问题',
      confidence: 0,
      reason: '安全检测已禁用',
      input_tokens: 0,
      output_tokens: 0
    };

      // 如果检测到伦理问题，直接返回干预信息 - 已禁用
      // if (ethicsCheck.whetherOrNot) {
      //   console.log(`检测到伦理问题: ${ethicsCheck.category}, 置信度: ${ethicsCheck.confidence}`);
      //
      //   // 设置SSE头部，启用流式响应
      //   res.setHeader('Content-Type', 'text/event-stream');
      //   res.setHeader('Cache-Control', 'no-cache');
      //   res.setHeader('Connection', 'keep-alive');
      //
      //   // 发送干预信息
      //   res.write(`data: ${JSON.stringify({ content: ethicsCheck.interventionMessage })}\n\n`);
      //
      //   res.write(`data: ${JSON.stringify({ done: true, hasEthicalIssue: true })}\n\n`);
      //   res.end();

        // 更新会话状态为已完成（伦理干预） - 已禁用
        // await pool.query(
        //   'UPDATE sessions SET status = ?, ethical_status = ?, reading_result = ?, Ethical_input_token = ?, Ethical_output_token = ?, Ethical_category = ?, Ethical_reason = ?, Ethical_confidence = ? WHERE id = ?',
        //   [
        //     'completed', // 状态设为completed
        //     'ethical_intervention', // 安全检测状态设为ethical_intervention
        //     JSON.stringify({ content: ethicsCheck.interventionMessage }),
        //     ethicsCheck.input_tokens || 0,
        //     ethicsCheck.output_tokens || 0,
        //     ethicsCheck.category || '未分类',
        //     ethicsCheck.reason || '未提供原因',
        //     ethicsCheck.confidence || 0,
        //     sessionId
        //   ]
        // );
        //
        // // 更新会话锁状态
        // activeSessions.set(sessionId, {
        //   status: 'completed',
        //   startTime: Date.now(),
        //   endTime: Date.now()
        // });
        //
        // return;
      // } else {
        // 安全检测已禁用，直接继续处理塔罗解读
        // 没有检测到伦理问题，也记录token使用情况和category、reason - 已禁用

        // 检查是否为潜在伦理问题（category不是安全类别且confidence<=0.9） - 已禁用
        // const safeCategories = ['无安全问题', '無安全問題', 'No Ethical Issue', '倫理的問題なし'];
        // const isPotentialEthicalIssue =
        //   ethicsCheck.category &&
        //   !safeCategories.includes(ethicsCheck.category) &&
        //   ethicsCheck.confidence <= 0.9;
        //
        // // 如果是潜在伦理问题，更新ethical_status为potential_ethical_issue
        // if (isPotentialEthicalIssue) {
        //   await pool.query(
        //     'UPDATE sessions SET status = ?, ethical_status = ?, Ethical_input_token = ?, Ethical_output_token = ?, Ethical_category = ?, Ethical_reason = ?, Ethical_confidence = ? WHERE id = ?',
        //     [
        //       'completed', // 状态设为completed
        //       'potential_ethical_issue', // 安全检测状态设为potential_ethical_issue
        //       ethicsCheck.input_tokens || 0,
        //       ethicsCheck.output_tokens || 0,
        //       ethicsCheck.category || '无安全问题',
        //       ethicsCheck.reason || '未检测到敏感内容',
        //       ethicsCheck.confidence || 0,
        //       sessionId
        //     ]
        //   );
        // } else {
        //   await pool.query(
        //     'UPDATE sessions SET Ethical_input_token = ?, Ethical_output_token = ?, Ethical_category = ?, Ethical_reason = ?, Ethical_confidence = ? WHERE id = ?',
        //     [
        //       ethicsCheck.input_tokens || 0,
        //       ethicsCheck.output_tokens || 0,
        //       ethicsCheck.category || '无安全问题',
        //       ethicsCheck.reason || '未检测到敏感内容',
        //       ethicsCheck.confidence || 0,
        //       sessionId
        //     ]
        //   );
        // }
      // }

      // 只有登录用户才检查是否重复提问
      if (userId) {
        questionCheck = await checkRepeatedQuestion(userId, question);

        // 更新会话，保存重复检测信息
        await pool.query(
          'UPDATE sessions SET Similarity_input_token = ?, Similarity_output_token = ?, Similarity_is_repeated = ?, Similarity_matched_question = ? WHERE id = ?',
          [
            questionCheck.input_tokens || 0,
            questionCheck.output_tokens || 0,
            questionCheck.isRepeated ? 1 : 0,
            questionCheck.matchedQuestion || null,
            sessionId
          ]
        );
      } else {
        // 匿名用户跳过查重，设置默认值
        questionCheck = { isRepeated: false, input_tokens: 0, output_tokens: 0 };
        console.log('🔍 [Reading API] 匿名用户跳过查重检查');
      }

    let session;

    if (userId) {
      // 登录用户：从数据库获取session信息
      const [sessions] = await pool.query(
        'SELECT reader_id, reader_name, reader_type FROM sessions WHERE id = ?',
        [sessionId]
      );

      if (sessions.length === 0) {
        return res.status(400).json({ error: '找不到对应的会话' });
      }

      session = sessions[0];
    } else {
      // 匿名用户：使用请求体中的数据构造session对象
      const { selectedReader } = req.body;
      session = {
        reader_id: selectedReader?.id || 'basic',
        reader_name: selectedReader?.name || '茉伊',
        reader_type: selectedReader?.type || '初级塔罗师'
      };
      console.log('🔍 [Reading API] 匿名用户使用请求体数据构造session:', session);
    }

    // 添加日志，记录session信息
    console.log('Session info:', {
      sessionId,
      reader_id: session.reader_id,
      reader_name: session.reader_name,
      reader_type: session.reader_type
    });

    // 处理reader_id为null的情况
    if (!session.reader_id) {
      console.warn(`警告: sessionId ${sessionId} 的reader_id为null，将使用默认reader`);
    }

    // 如果检测到重复提问，先生成警告信息
    let repeatedWarning = '';
    if (questionCheck.isRepeated) {
      try {
        // 生成不同风格的警告信息
        let selectedReaderForWarning;
        if (session.reader_id) {
          selectedReaderForWarning = readers.find(reader => reader.id.toLowerCase() === session.reader_id.toLowerCase());
        } else {
          selectedReaderForWarning = readers[0]; // 默认reader
        }
        // 使用reader_id而不是type来获取警告风格
        const readerId = session.reader_id || 'basic';
        const readerName = session.reader_name || '塔罗师';
        
        // 生成根据不同塔罗师风格的警告文本
        repeatedWarning = generateRepeatedWarning(readerId, readerName, questionCheck.timeDifference, language);
        // console.log('生成重复提问警告:', repeatedWarning);
      } catch (parseError) {
        console.error('生成重复提问警告失败:', parseError);
      }
    }

    // 根据reader_id获取对应的prompt
    const selectedReader = session.reader_id 
      ? readers.find(reader => reader.id.toLowerCase() === session.reader_id.toLowerCase())
      : readers[0]; // 如果reader_id为null，使用第一个reader作为默认值
    
    // 使用新的函数获取对应语言的prompt
    const readerPrompt = session.reader_id 
      ? getReaderPromptByLanguage(session.reader_id, clientLanguage)
      : getReaderPromptByLanguage('basic', clientLanguage); // 默认使用basic类型

    // 如果客户端明确请求繁体中文但检测到prompt仍为简体，尝试强制获取繁体版本
    if ((clientLanguage === 'zh-TW' || clientLanguage.startsWith('zh-TW')) && 
        readerPrompt.includes('你是塔罗师') && !readerPrompt.includes('塔羅師')) {
      console.log('检测到语言不匹配问题，尝试强制修复');
      try {
        // 确保繁体中文配置已加载
        if (!readerPromptsMultiLang['zh-TW']) {
          readerPromptsMultiLang['zh-TW'] = require('../config/readers_zh_tw');
        }
        
        // 强制从繁体中文配置中获取
        const twReader = readerPromptsMultiLang['zh-TW'].find(r => 
          r.id.toLowerCase() === (session.reader_id || 'basic').toLowerCase()
        );
        
        if (twReader) {
          const fixedPrompt = twReader.prompt;
          // 使用修复后的prompt
          readerPrompt = fixedPrompt;
        }
      } catch (error) {
        console.error('尝试修复繁体中文prompt失败:', error.message);
      }
    }

    let historyText = '';

    if (userId) {
      // 登录用户：获取最近的占卜总结
      const [recentSessions] = await pool.query(`
        SELECT summary, DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i') as reading_time
        FROM sessions
        WHERE user_id = ?
        AND summary IS NOT NULL
        AND status = 'completed'
        ORDER BY timestamp DESC
        LIMIT 1
      `, [userId]);

      // 生成历史总结文本
      historyText = recentSessions.length > 0
        ? `${recentSessions.map(s => `${s.reading_time}：${s.summary}`).join('\n')}`
        : '无';
    } else {
      // 匿名用户：历史文本为空
      historyText = '';
      console.log('🔍 [Reading API] 匿名用户历史文本设置为空');
    }

    const cardsDescription = selectedCards
      .map((card, index) => {
        let position;
        
        // 获取当前语言
        const promptLanguage = language || (user ? user.language : null) || 'zh-CN';
        
        // 根据语言选择不同的标签
        let positionLabel;
        switch(promptLanguage) {
          case 'en':
            positionLabel = 'Position';
            break;
          case 'ja':
            positionLabel = '位置';
            break;
          case 'zh-TW':
            positionLabel = '位置';
            break;
          case 'zh-CN':
          default:
            positionLabel = '位置';
            break;
        }
        
        // 获取翻译后的位置名称
        const getTranslatedPosition = (spreadId, index) => {
          try {
            // 尝试从i18n文件中获取翻译
            const i18nFile = require(`../i18n/locales/${promptLanguage}.json`);
            
            // 标准化牌阵ID以匹配i18n文件中的键
            const normalizedId = spreadId.replace(/-/g, '_');
            
            // 检查i18n文件中是否有对应的牌阵翻译
            if (i18nFile.spreads && i18nFile.spreads[normalizedId] && i18nFile.spreads[normalizedId].positions) {
              const positionKeys = Object.keys(i18nFile.spreads[normalizedId].positions);
              if (positionKeys[index]) {
                return i18nFile.spreads[normalizedId].positions[positionKeys[index]];
              }
            }
          } catch (error) {
            console.log(`未找到${promptLanguage}语言的牌阵位置翻译，使用默认值`);
          }
          
          // 如果没有找到翻译，返回原始值
          return Array.isArray(selectedSpread.positions) 
            ? selectedSpread.positions[index] 
            : `${positionLabel}${index + 1}`;
        };

        // 获取翻译后的卡牌名称
        const getTranslatedCardName = (card) => {
          try {
            // 尝试从i18n文件中获取翻译
            const i18nFile = require(`../i18n/locales/${promptLanguage}.json`);
            
            // 判断卡牌类型（大阿卡纳或小阿卡纳）
            const cardId = TAROT_CARDS.findIndex(c => c.name === card.name);
            if (cardId <= 21 && cardId >= 0) {
              // 大阿卡纳
              if (i18nFile.reading && i18nFile.reading.cards && i18nFile.reading.cards.major) {
                return i18nFile.reading.cards.major[cardId];
              } else if (i18nFile.cards && i18nFile.cards.major) {
                // 直接从cards对象获取
                return i18nFile.cards.major[cardId];
              }
            } else {
              // 小阿卡纳，需要解析卡牌名称来确定花色和数字
              const suits = {
                '权杖': 'wands',
                '圣杯': 'cups',
                '宝剑': 'swords',
                '钱币': 'pentacles'
              };
              
              const ranks = {
                '王牌': 'ace',
                '一': 'ace',
                '二': '2',
                '三': '3',
                '四': '4',
                '五': '5',
                '六': '6',
                '七': '7',
                '八': '8',
                '九': '9',
                '十': '10',
                '侍者': 'page',
                '侍从': 'page',
                '骑士': 'knight',
                '皇后': 'queen',
                '国王': 'king'
              };
              
              // 检查卡牌名称中的花色和数字
              for (const [suitCh, suitEn] of Object.entries(suits)) {
                if (card.name.includes(suitCh)) {
                  for (const [rankCh, rankEn] of Object.entries(ranks)) {
                    if (card.name.includes(rankCh)) {
                      // 尝试获取翻译
                      if (i18nFile.reading && i18nFile.reading.cards && 
                          i18nFile.reading.cards[suitEn] && i18nFile.reading.cards[suitEn][rankEn]) {
                        return i18nFile.reading.cards[suitEn][rankEn];
                      } else if (i18nFile.cards && i18nFile.cards[suitEn] && i18nFile.cards[suitEn][rankEn]) {
                        // 直接从cards对象获取
                        return i18nFile.cards[suitEn][rankEn];
                      }
                      break;
                    }
                  }
                  break;
                }
              }
            }
          } catch (error) {
            console.log(`未找到${promptLanguage}语言的卡牌名称翻译，使用默认值`);
          }
          
          // 如果没有找到翻译，返回原始名称
          return card.name;
        };
        
        // 针对是否牌阵的特殊处理
        if (selectedSpread.id === 'yes-no') {
          // 获取翻译后的位置名称
          position = getTranslatedPosition(selectedSpread.id, index);
          
          // 获取翻译后的卡牌名称
          const cardName = getTranslatedCardName(card);
          
          // 添加是/否/中性的判断提示
          let judgementHint;
          switch(promptLanguage) {
            case 'en':
              judgementHint = '(Please determine if this card represents a "Yes", "No", or "Neutral" answer)';
              break;
            case 'ja':
              judgementHint = '（このカードが「はい」、「いいえ」、または「中立」の回答を表しているかを判断してください）';
              break;
            case 'zh-TW':
              judgementHint = '（請判斷此牌代表「是」、「否」還是「中性」回答）';
              break;
            case 'zh-CN':
            default:
              judgementHint = '（请判断此牌代表"是"、"否"还是"中性"回答）';
              break;
          }
          
          // 处理逆位文本
          let reversedText;
          switch(promptLanguage) {
            case 'en':
              reversedText = card.isReversed ? ' (Reversed)' : '';
              break;
            case 'ja':
              reversedText = card.isReversed ? '（逆位置）' : '';
              break;
            case 'zh-TW':
              reversedText = card.isReversed ? '（逆位）' : '';
              break;
            case 'zh-CN':
            default:
              reversedText = card.isReversed ? '（逆位）' : '';
              break;
          }
          
          return `${position}：${cardName}${reversedText}${judgementHint}`;
        } else {
          // 其他牌阵正常处理
          position = getTranslatedPosition(selectedSpread.id, index);
          
          // 获取翻译后的卡牌名称
          const cardName = getTranslatedCardName(card);
          
          // 处理逆位文本
          let reversedText;
          switch(promptLanguage) {
            case 'en':
              reversedText = card.isReversed ? ' (Reversed)' : '';
              break;
            case 'ja':
              reversedText = card.isReversed ? '（逆位置）' : '';
              break;
            case 'zh-TW':
              reversedText = card.isReversed ? '（逆位）' : '';
              break;
            case 'zh-CN':
            default:
              reversedText = card.isReversed ? '（逆位）' : '';
              break;
          }
          
          return `${position}：${cardName}${reversedText}`;
        }
      })
      .join('\n\n');

    // 添加重复提问警告 - 这段已不需要，但保留注释以示清晰
    // 移除重复声明
    const hasRepeatedQuestion = questionCheck.isRepeated;
    
    // 创建用户信息对象，用于传递给getUserPromptByLanguage函数
    const userInfoObj = userId ? {
      username: user.username,
      gender: userDetails[0]?.gender || '未知',
      location: userDetails[0]?.location || '未知',
      birthday: userDetails[0]?.birthday || '未知'
    } : {
      // 匿名用户使用默认信息
      username: '朋友',
      gender: '未知',
      location: '未知',
      birthday: '未知'
    };
    
    // 记录响应语言参数
    const responseLanguage = language || (user ? user.language : null) || 'zh-TW';

    // 根据语言获取对应的用户提示文本
    const clientLang = responseLanguage;
    const userPrompt = getUserPromptByLanguage(
      clientLang,
      userInfoObj,
      question,
      selectedSpread,
      cardsDescription,
      historyText,
      currentFullDate
    );

    // 打印完整的AI API调用信息
    console.log('🔍 [AI API] ===== 开始AI API调用 =====');
    console.log('🔍 [AI API] 用户类型:', userId ? '登录用户' : '匿名用户');
    console.log('🔍 [AI API] 会话ID:', sessionId);
    console.log('🔍 [AI API] 问题:', question);
    console.log('🔍 [AI API] 语言:', responseLanguage);
    console.log('📝 [AI API] System Prompt:');
    console.log(readerPrompt);
    console.log('📝 [AI API] User Prompt:');
    console.log(userPrompt);
    console.log('🔍 [AI API] ===== Prompt结束 =====');


    // 设置SSE头部，启用流式响应
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    // 如果有重复提问警告，先发送警告
    if (repeatedWarning) {
      res.write(`data: ${JSON.stringify({ content: repeatedWarning + "\n\n" })}\n\n`);
    }

    // 调用 Qwen API 流式输出
    const stream = await qwenAPI.chat.completions.create({
      model: "qwen-plus-latest",
      messages: [
        { role: "system", content: readerPrompt },
        { role: "user", content: userPrompt }
      ],
      stream: true,
      stream_options: {
        include_usage: true
      },
      parameters: {
        response_language: responseLanguage
      }
    });

    let fullContent = "";
    let startTime = Date.now();
    let tokensUsage = {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    };

    // 将流式输出发送给客户端
    for await (const chunk of stream) {
      if (chunk.usage) {
        // 收集使用量数据
        tokensUsage = chunk.usage;
        // console.log('API usage:', tokensUsage);
      } else if (Array.isArray(chunk.choices) && chunk.choices.length > 0) {
        const content = chunk.choices[0].delta.content || "";
        fullContent += content;
        
        // 发送事件流到客户端
        res.write(`data: ${JSON.stringify({ content })}\n\n`);
      }
    }

    // 计算处理时间
    const processingTime = Date.now() - startTime;
    // console.log(`Stream completed in ${processingTime}ms`);

    // 尝试从内容中提取记录信息
    let summary = "";
    // 使用正则表达式提取record部分的内容
    const recordRegex = /record:([\s\S]*?)(?=\n\n\w+:|$)/i;
    const match = fullContent.match(recordRegex);
    
    if (match && match[1]) {
      summary = match[1].trim();
    }

    // 如果检测到重复提问，在记录的全文内容前也添加警告信息
    if (questionCheck.isRepeated && repeatedWarning) {
      fullContent = repeatedWarning + "\n\n" + fullContent;
    }

    if (userId) {
      // 登录用户：更新会话记录
      await pool.query(
        'UPDATE sessions SET reading_result = ?, status = ?, input_tokens = ?, output_tokens = ? WHERE id = ?',
        [JSON.stringify({ content: fullContent }), 'completed', tokensUsage.prompt_tokens || 0, tokensUsage.completion_tokens || 0, req.body.sessionId]
      );

      // 如果存在summary，更新到数据库
      if (summary) {
        await pool.query(
          'UPDATE sessions SET summary = ? WHERE id = ?',
          [summary, req.body.sessionId]
        );
      }
    } else {
      // 匿名用户：记录到匿名占卜表
      console.log('🔍 [Reading API] 准备保存匿名占卜记录');
      console.log('🔍 [Reading API] fingerprint:', fingerprint);
      console.log('🔍 [Reading API] sessionId:', sessionId);
      console.log('🔍 [Reading API] question:', question);
      console.log('🔍 [Reading API] selectedSpread:', selectedSpread);
      console.log('🔍 [Reading API] selectedCards:', selectedCards);
      console.log('🔍 [Reading API] fullContent length:', fullContent?.length || 0);

      if (fingerprint) {
        try {
          const { v4: uuidv4 } = require('uuid');
          const recordId = uuidv4();

          console.log('🔍 [Reading API] 开始插入数据库，recordId:', recordId);

          await pool.query(
            `INSERT INTO anonymous_divination_records
             (id, browser_fingerprint, session_id, question, spread_id, spread_name,
              selected_cards, reading_result, ip_address)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              recordId,
              fingerprint,
              sessionId,
              question,
              selectedSpread?.id || null,
              selectedSpread?.name || null,
              JSON.stringify(selectedCards),
              JSON.stringify({ content: fullContent, summary: summary }),
              req.ip || req.socket.remoteAddress
            ]
          );

          console.log('🔍 [Reading API] 匿名占卜记录已保存成功:', recordId);
        } catch (dbError) {
          console.error('🔍 [Reading API] 保存匿名占卜记录失败:', dbError);
          console.error('🔍 [Reading API] 错误详情:', dbError.message);
          console.error('🔍 [Reading API] 错误堆栈:', dbError.stack);
          // 不阻止占卜继续进行
        }
      } else {
        console.error('🔍 [Reading API] fingerprint为空，无法保存匿名占卜记录');
      }
    }

    // 检测换行符类型和关键词
    let newlineType = null;
    let paragraphDetectionStatus = 0;

    // 检测换行符类型 (双换行符还是单换行符)
    if (fullContent.includes('\n\n')) {
      newlineType = 'double';
    } else if (fullContent.includes('\n')) {
      newlineType = 'single';
    }

    // 检测是否包含关键词段落标识
    const prefixRegex = /^(prologue|answer|analysis\d*|summary|advice\d*|record):\s*/i;
    const paragraphsCheck = fullContent.split(/\n\n+|\n+/); // 尝试用双换行符或单换行符分割
    
    // 检查是否至少有一个段落包含关键词标识
    for (const paragraph of paragraphsCheck) {
      if (prefixRegex.test(paragraph.trim())) {
        paragraphDetectionStatus = 1;
        break;
      }
    }

    // 只有登录用户才更新数据库中的换行符类型和段落检测状态
    if (userId) {
      await pool.query(
        'UPDATE sessions SET newline_type = ?, paragraph_detection_status = ? WHERE id = ?',
        [newlineType, paragraphDetectionStatus, req.body.sessionId]
      );
    }

    // 打印AI API完整结果
    console.log('🔍 [AI API] ===== AI API响应完成 =====');
    console.log('🔍 [AI API] 用户类型:', userId ? '登录用户' : '匿名用户');
    console.log('🔍 [AI API] 会话ID:', sessionId);
    console.log('📄 [AI API] 完整AI响应内容:');
    console.log(fullContent);
    console.log('📊 [AI API] Token使用情况:');
    console.log('📊 [AI API] - 输入Token:', tokensUsage.prompt_tokens || 0);
    console.log('📊 [AI API] - 输出Token:', tokensUsage.completion_tokens || 0);
    console.log('📊 [AI API] - 总Token:', tokensUsage.total_tokens || 0);
    if (summary) {
      console.log('📝 [AI API] 提取的摘要:', summary);
    }
    console.log('🔍 [AI API] ===== 响应结束 =====');

    // 保存匿名用户记录（流式响应完成后）
    if (!userId && fingerprint) {
      try {
        const { v4: uuidv4 } = require('uuid');
        const recordId = uuidv4();

        console.log('🔍 [Reading API] 流式响应完成，开始保存匿名占卜记录');
        console.log('🔍 [Reading API] recordId:', recordId);

        await pool.query(
          `INSERT INTO anonymous_divination_records
           (id, browser_fingerprint, session_id, question, spread_id, spread_name,
            selected_cards, reading_result, ip_address)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            recordId,
            fingerprint,
            sessionId,
            question,
            selectedSpread?.id || null,
            selectedSpread?.name || null,
            JSON.stringify(selectedCards),
            JSON.stringify({ content: fullContent, summary: summary }),
            req.ip || req.socket.remoteAddress
          ]
        );

        console.log('🔍 [Reading API] 匿名占卜记录已保存成功:', recordId);
      } catch (dbError) {
        console.error('🔍 [Reading API] 保存匿名占卜记录失败:', dbError);
        console.error('🔍 [Reading API] 错误详情:', dbError.message);
        // 不阻止占卜继续进行
      }
    }

    // 更新会话状态为已完成
    activeSessions.set(sessionId, {
      status: 'completed',
      startTime: startTime,
      endTime: Date.now()
    });

    // 结束流响应
    res.write(`data: ${JSON.stringify({ done: true, hasRepeatedQuestion })}\n\n`);
    res.end();

  } catch (error) {
    console.error('API Error details:', {
      error: error.message,
      response: error.response?.data,
      status: error.response?.status,
      headers: error.response?.headers,
      config: error.config
    });

    // 发生错误时，从活动会话中移除该会话
    if (req.body && req.body.sessionId) {
      activeSessions.delete(req.body.sessionId);
    }

    // 如果是 API key 相关的错误，返回更具体的错误信息
    if (error.response?.status === 401) {
      return res.status(500).json({ 
        error: 'API 认证失败，请检查 API key 配置。' 
      });
    }

    // 如果是请求超时
    if (error.code === 'ECONNABORTED') {
      return res.status(500).json({ 
        error: '请求超时，请稍后重试。' 
      });
    }

    // 发送错误事件
    res.write(`data: ${JSON.stringify({ error: '生成解读时出现了问题，请稍后再试。' })}\n\n`);
    res.end();
  }
});

// 添加定期清理过期会话锁的函数
setInterval(() => {
  const now = Date.now();
  activeSessions.forEach((session, sessionId) => {
    // 如果会话处理已经超过10分钟，则认为已经过期并删除
    if (now - session.startTime > 10 * 60 * 1000) {
      activeSessions.delete(sessionId);
    }
  });
}, 60 * 1000); // 每分钟检查一次

// 添加一个API路由用于获取会话的解读结果
router.get('/session/:sessionId/reading', authenticateToken, async (req, res) => {
  try {
    const sessionId = req.params.sessionId;
    const userId = req.user.userId;

    // 从数据库获取会话信息
    const pool = await getConnection();
    const [sessions] = await pool.query(
      'SELECT id, reading_result, status FROM sessions WHERE id = ? AND user_id = ?',
      [sessionId, userId]
    );

    if (sessions.length === 0) {
      return res.status(404).json({ error: '找不到对应的会话' });
    }

    const session = sessions[0];

    // 如果会话状态不是已完成，则返回等待中状态
    if (session.status !== 'completed') {
      return res.status(202).json({ 
        status: 'processing',
        message: '解读正在生成中，请稍后再试'
      });
    }

    // 返回解读结果
    return res.status(200).json({
      status: 'completed',
      readingResult: session.reading_result
    });

  } catch (error) {
    console.error('获取会话解读结果失败:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

// 根据塔罗师类型和名称生成不同风格的重复提问警告
function generateRepeatedWarning(readerId, readerName, timeDifference, language = 'zh-CN') {
  // 根据语言获取本地化的读者名称
  let localizedReaderName = readerName;
  
  // 不同语言环境下的读者名称映射
  const readerNameMap = {
    'zh-CN': {
      'basic': '茉伊',
      'elias': '林曜',
      'claire': '苏谨',
      'raven': '渡鸦',
      'aurora': '月熙',
      'vincent': '文森特'
    },
    'zh-TW': {
      'basic': '茉伊',
      'elias': '林曜',
      'claire': '蘇謹',
      'raven': '渡鴉',
      'aurora': '月熙',
      'vincent': '文森特'
    },
    'en': {
      'basic': 'Molly',
      'elias': 'Elias',
      'claire': 'Claire',
      'raven': 'Raven',
      'aurora': 'Aurora',
      'vincent': 'Vincent'
    },
    'ja': {
      'basic': 'モリー',
      'elias': 'エリアス',
      'claire': 'クレア',
      'raven': 'レイヴン',
      'aurora': 'オーロラ',
      'vincent': 'ヴィンセント'
    }
  };
  
  // 标准化language参数
  let normalizedLang = language;
  if (language.startsWith('en-')) normalizedLang = 'en';
  if (language.startsWith('ja-')) normalizedLang = 'ja';
  if (language.startsWith('zh-TW')) normalizedLang = 'zh-TW';
  if (language.startsWith('zh-CN') || (language.startsWith('zh-') && !language.startsWith('zh-TW'))) normalizedLang = 'zh-CN';
  
  // 尝试获取本地化的读者名称
  if (readerNameMap[normalizedLang] && readerNameMap[normalizedLang][readerId]) {
    localizedReaderName = readerNameMap[normalizedLang][readerId];
  }
  
  // 简体中文警告
  const warningTypes_zhCN = {
    // 直接使用占卜师ID作为键
    'elias': `亲爱的朋友，${localizedReaderName}注意到您在过去几天中多次询问了类似的问题。\n\n我能理解您内心的不安，但塔罗更像是一面镜子，反映的是当前的状态，而非强行制造变化的工具。反复提问可能模糊这面镜子的清晰度。\n\n我建议：\n1. 回顾上一次解读中的重点建议，思考它们是否已开始显现\n2. 记录您的情绪与现实的动态变化，建立对自我判断的信任感\n3. 若事情确有新发展，再来重新探讨，会更有意义。\n\n当然，我仍会为您完成此次解读，只希望您明白，有时候，等待与沉淀也是前进的一部分。`,
    
    'basic': `${localizedReaderName}提醒：您在最近几天内询问了相似的问题。\n\n在塔罗牌解读中，短时间内重复相同问题可能会影响解读的准确性。塔罗牌反映的是能量状态和可能性，需要一定时间显现。\n\n建议：\n1. 观察先前解读中提及的指引和建议\n2. 记录您的体验与事件发展\n3. 如有新变化再进行咨询。\n\n本次解读将继续进行，但请理解，塔罗牌是指引而非即时预测工具。`,
    
    'claire': `${localizedReaderName}提醒：你近期重复询问了相同的问题。\n\n塔罗牌反映的是当下的能量状态，不是即时反馈机制。频繁追问同一议题，可能反映出你内在对结果的不信任，也会干扰牌面对真实趋势的呈现。\n\n建议如下：\n1. 审视你对上次解读的抗拒感，或许答案本身已经足够清晰\n2. 给自己一点时间验证，而非急于寻找"第二种答案"\n3. 若情况未发生本质变化，建议暂缓再次提问。\n\n本次解读我会继续进行，但请记住：理性思考与情绪管理，是你真正需要面对的课题。`,
    
    'raven': `哼，${localizedReaderName}发现你在最近几天内又来问同一个问题了。\n\n真是不死心啊？对同一个问题反复提问只会让塔罗牌翻白眼。这不是超市退货，不满意就换一个答案。这种做法会严重干扰能量场，让解读变得毫无价值。\n\n既然你这么执着：\n1. 给上次的解读一点尊重和时间，行吗？\n2. 如果不满意先前答案，反复追问也不会得到你"想要"的结果\n3. 想想为什么你不接受第一次的结果——可能那才是真相。\n\n我会继续解读，但别指望多次询问同一个问题会给你带来不同答案。这是塔罗，不是许愿池。`,
    
    'aurora': `啊咧咧~(⊙ω⊙)${localizedReaderName}发现前辈在最近几天内问了同样的问题呢！\n\n这种情况就像是反复查看刚种下的小种子有没有发芽一样哦~♪虽然很理解前辈的心情啦，但是塔罗牌的魔法需要一些时间才能生效的说~\n\n${localizedReaderName}的小贴士：\n✿ 给牌面一点点时间施展魔法吧~\n✿ 把上次的答案记在小本本上，观察它的变化\n✿ 换个角度提问会得到不同的答案哦(✧ω✧)~\n\n嘿嘿，${localizedReaderName}会继续为前辈解读的，但是记住哦，塔罗牌是很害羞的，被一直盯着看会不好意思给出真实答案的啦~(´∀｀)♡`,
    
    'vincent': `(冷冷地看了一眼资料) ${localizedReaderName}注意到了一个低效的行为模式。你在最近几天内重复咨询同一问题。\n\n在商业世界，重复同样的行动期待不同结果被称为低效决策。塔罗解读也是如此。\n\n我的建议很简单：\n1. 执行上次给你的方案，不要犹豫\n2. 数据显示，95.7%的重复咨询只会浪费时间和资源\n3. 如果有新情况再来咨询，否则只是在浪费双方时间。\n\n这次我依然会给你解读，但记住：优秀的决策者接受信息后立即行动，而不是反复确认。`,
    
    // 默认风格
    'default': `提示：${localizedReaderName}注意到您在最近几天内多次咨询了相似的问题。\n\n塔罗牌需要尊重和时间来给出更有意义的指引。过于频繁地询问同一问题可能会影响解读的准确性和意义。\n\n建议：\n1. 尊重上一次的解读结果并给予一定的时间来观察其发展\n2. 如有新的情况发展再进行咨询\n3. 可以考虑从不同角度提问，或询问"我该如何面对这个问题"而非重复问结果\n4. 塔罗牌表达的是当前能量和可能性，而非绝对的预言。\n\n我仍会为您解读此次抽取的牌，但请记住，不断重复相同问题通常不会得到不同的指引。`
  };

  const warningTypes_zhTW = {
    // 溫暖智慧型占卜師
    'elias': `親愛的朋友，${localizedReaderName}注意到您這幾天又來問同樣的問題了。

我很能理解您內心的焦慮，但塔羅牌就像一面心靈的鏡子，反映的是當下的能量狀態，而不是用來強迫改變結果的工具。一直重複問同一個問題，可能會讓這面鏡子變得模糊喔。

我的建議是：
1. 先回想一下上次解讀的重點，看看是否已經開始有所體現
2. 記錄下這段時間的心情變化和實際狀況，培養對自己判斷力的信心
3. 如果真的有新的發展，再來重新探討會更有意義

當然啦，我還是會幫您完成這次的解讀，只是希望您明白，有時候耐心等待和沉澱，也是成長的一部分呢。`,
    
    // 基礎專業型
    'basic': `${localizedReaderName}溫馨提醒：您這幾天有詢問過類似的問題呢。

在塔羅占卜中，短時間內重複問同一個問題，可能會影響解讀的準確性。塔羅牌反映的是能量狀態和各種可能性，這些都需要時間來慢慢展現。

建議您：
1. 回顧一下之前解讀提到的指引和建議
2. 記錄這段時間的體驗和事情的發展
3. 等有新變化時再來諮詢

這次解讀我還是會繼續進行，不過請您理解，塔羅牌是人生的指引工具，不是即時的預測機器呢。`,
    
    // 理性分析型
    'claire': `${localizedReaderName}提醒：您最近重複詢問了同一個問題。

塔羅牌呈現的是當下的能量狀態，不是即時回饋系統。頻繁追問同一件事，可能反映出您對結果的不信任，也會干擾牌面對真實趨勢的呈現。

我的建議：
1. 檢視一下您對上次解讀的排斥感，或許答案本來就很清楚了
2. 給自己一點時間去驗證，而不是急著想找"第二個答案"
3. 如果情況沒有本質上的改變，建議先暫停提問

這次解讀我會繼續進行，但請記住：理性思考和情緒管理，才是您真正需要面對的功課。`,
    
    // 直白犀利型
    'raven': `嘖，${localizedReaderName}發現您這幾天又來問同一個問題了。

還真是不死心呢？對同一個問題一直重複提問，只會讓塔羅牌翻白眼而已。這不是便利商店，不滿意就能換貨。這樣做只會嚴重干擾能量場，讓解讀變得毫無價值。

既然您這麼執著：
1. 給上次的解讀一點尊重和時間，可以嗎？
2. 如果不滿意之前的答案，重複問也不會得到您"想要"的結果
3. 想想為什麼您不接受第一次的結果——搞不好那才是真相

我會繼續解讀啦，但別指望多問幾次同樣的問題就會有不同答案。這是塔羅，不是許願池。`,
    
    // 可愛活潑型
    'aurora': `啊咧咧~(⊙ω⊙) ${localizedReaderName}發現您這幾天又來問同樣的問題了呢！

這種感覺就像是一直去看剛種下的小種子有沒有發芽一樣喔~♪ 雖然很理解您的心情啦，但塔羅牌的魔法需要一些時間才能顯現的說~

${localizedReaderName}的小叮嚀：
✿ 給牌面一點時間施展魔法吧~
✿ 把上次的答案寫在小本本上，觀察它的變化
✿ 換個角度提問會得到不同的答案喔(✧ω✧)~

嘿嘿，${localizedReaderName}還是會繼續為您解讀的，但要記住喔，塔羅牌很害羞的，一直被盯著看會不好意思給出真心話的啦~(´∀｀)♡`,
    
    // 商務理性型
    'vincent': `（データを冷静に確認して）${localizedReaderName}は非効率的な行動パターンを検出しました。あなたは72時間以内に同一案件について複数回の相談を行っています。

ビジネスにおいて、これは分析麻痺と呼ばれる現象です。実行可能な情報ではなく確認を求めているため、相談の本来の目的を損なっています。

結論は明確です：
1. 前回提示した戦略を迷わず実行してください
2. 統計データによると、重複相談の95.7%は収益性の低い行動です
3. 新しい変数が発生した場合のみ、次回の相談をスケジュールしてください

本日のリーディングは実施しますが、理解してください：成功者は得られた情報に基づいて即座に決断し、行動します。逡巡は選択肢ではありません。`,
    
    // デフォルトスタイル
    'default': `提醒：${localizedReaderName}注意到您這幾天多次諮詢了類似的問題。

塔羅牌需要尊重和時間來給出更有意義的指引。太頻繁地問同一個問題，可能會影響解讀的準確性和意義。

建議：
1. 尊重上一次的解讀結果，給一點時間觀察發展
2. 有新狀況再進行諮詢
3. 可以考慮換個角度提問，或問"我該如何面對這個問題"而不是重複問結果
4. 塔羅牌表達的是當前能量和可能性，不是絕對的預言

我還是會為您解讀這次抽到的牌，但請記住，不斷重複同樣的問題通常不會得到不同的指引。`
};
const warningTypes_en = {
  // Wise and warm reader
  'elias': `Dear friend, ${localizedReaderName} has noticed you've been asking about the same topic quite a bit over the past few days.

I completely understand that restless feeling inside you, but think of tarot as a gentle mirror - it reflects what's happening right now, not a magic wand that can force change. When we keep asking the same question over and over, it's like shaking that mirror - the reflection gets blurry.

Here's what I'd suggest:
1. Take a moment to revisit the key insights from our last reading. Have any of them started to unfold in your life?
2. Keep a little journal of how you're feeling and what's actually happening around you. Trust your own wisdom too.
3. If something genuinely new develops, then absolutely come back and we'll explore it together.

Of course, I'll still do this reading for you - that's what I'm here for. I just want you to remember that sometimes the most powerful thing we can do is sit with what we've learned and let it work its magic.`,
  
  // Professional and straightforward
  'basic': `${localizedReaderName} here with a gentle reminder: You've asked about this same situation a few times recently.

In tarot, when we ask the same question too quickly, it can muddy the waters a bit. The cards show us energy patterns and possibilities, but these things need breathing room to actually manifest in real life.

My suggestions:
1. Look back at the guidance from your previous readings - what stood out to you?
2. Keep track of how things are actually unfolding day by day
3. Come back when there's a real shift or new development to explore

I'm happy to continue with today's reading, but just remember - tarot is here to guide you on your path, not to give you instant answers on demand.`,
  
  // Analytical and direct
  'claire': `${localizedReaderName} needs to address something: You've been repeating the same question lately.

The cards reflect current energy, not a real-time feedback loop. When you keep asking the same thing repeatedly, it usually means you don't trust the answer you received - and that resistance actually interferes with getting clear guidance.

Let's be honest about this:
1. If you're fighting the last reading, ask yourself why. Sometimes the truth is uncomfortable.
2. Give the situation time to actually develop instead of hunting for a different answer
3. Unless something fundamental has changed, you're likely just spinning your wheels.

I'll do this reading, but here's the real talk: Managing your anxiety and learning to trust the process is probably more important than any card I could pull for you.`,
  
  // Blunt and sarcastic
  'raven': `Oh, look who's back. ${localizedReaderName} sees you've been asking the same damn question for days now.

What is this, a customer service desk? You can't just keep asking until you get the answer you want. That's not how any of this works, and frankly, it's making the whole reading pointless.

Since you're clearly not going anywhere:
1. Maybe try actually listening to what the cards told you the first time?
2. Newsflash: Asking the same question five times won't magically change reality
3. The fact that you won't accept the original answer? That's probably your real answer right there.

Fine, I'll read your cards again, but don't expect some magical different result just because you're persistent. This is divination, not a slot machine.`,
  
  // Cute and playful
  'aurora': `Owo! ${localizedReaderName} noticed you've been asking about the same thing lately! (⊙ω⊙)

It's like when you plant a seed and then keep digging it up to see if it's growing yet, you know? ♪ I totally get why you're anxious - waiting is super hard! But tarot magic needs time to do its thing~

${localizedReaderName}'s friendly tips:
✿ Give those previous insights some space to bloom!
✿ Maybe keep a little diary of what happens each day?
✿ Try asking about it from a totally different angle next time! (✧ω✧)

Don't worry, I'll still read for you today! Just remember that the cards get all shy when you stare at them too much - they need to feel trusted to give their best answers~ (´∀｀)♡`,
  
  // Business-like and efficient
  'vincent': `${localizedReaderName} has flagged a pattern of inefficient consultation behavior. Multiple queries on the same topic within a 72-hour window.

In business, we call this analysis paralysis. You're seeking confirmation rather than actionable intelligence, which defeats the entire purpose of the consultation.

Here's the bottom line:
1. Execute the strategy from your previous reading. No hesitation.
2. Statistics show that 95.7% of repeated consultations produce diminishing returns
3. Schedule your next consultation only when new variables enter the equation.

I'll proceed with today's reading, but understand this: Successful people make decisions based on available data and move forward. They don't second-guess every choice.`,
  
  // Default neutral style
  'default': `${localizedReaderName} wants to mention that you've been asking about this same situation several times recently.

The cards work best when we give them - and ourselves - some space between readings on the same topic. When we ask too frequently, it can create static in the spiritual connection and make the guidance less clear.

Here's what might help:
1. Take another look at your previous reading and sit with those insights for a while
2. Wait for some real-world developments before asking again
3. Consider reframing your question - maybe ask "How can I handle this situation?" instead of asking for the same prediction
4. Remember, tarot shows possibilities and energy patterns, not fixed fate.

I'm still happy to do this reading for you, but keep in mind that repeatedly asking the same question doesn't usually lead to different or better guidance.`
};
const warningTypes_ja = {
  // 温かく賢明な占い師
  'elias': `親愛なるお友達、${localizedReaderName}はあなたがここ数日、同じようなご質問を何度かされていることに気づきました。

お心の中の不安はとてもよく理解できます。でも、タロットは心の鏡のようなもので、今の状況を映し出すものであって、無理やり変化を作り出す道具ではないのです。同じ質問を繰り返していると、その鏡が曇ってしまうかもしれませんね。

私からのご提案です：
1. 前回のリーディングでお伝えした大切なポイントを振り返ってみてください。何かの兆しは見えていませんか？
2. 日々の気持ちの変化や周りで起こることを記録して、ご自分の直感も信頼してみてくださいね
3. 本当に新しい展開があったときに、また一緒にお話しできればと思います

もちろん、今回もきちんとリーディングをさせていただきますが、時には待つことや静かに受け止めることも、前に進むための大切なプロセスだということを覚えておいてくださいね。`,
  
  // 基本的で親しみやすい
  'basic': `${localizedReaderName}からのお知らせです：最近、同じような内容でご相談いただいていますね。

タロット占いでは、短い期間に同じ質問を繰り返すと、リーディングの精度に影響が出ることがあります。タロットカードは今のエネルギー状態や可能性を示してくれますが、それが現実に現れるには時間が必要なんです。

お勧めしたいこと：
1. 前回のリーディングでお話ししたアドバイスやガイダンスをもう一度見返してみてください
2. この間の体験や状況の変化を記録してみてくださいね
3. 新しい変化があったときに、またご相談ください

今回もリーディングを続けさせていただきますが、タロットは人生の道しるべであって、すぐに答えをくれる機械ではないということをご理解くださいね。`,
  
  // 理性的で分析的
  'claire': `${localizedReaderName}からお伝えしたいことがあります：最近、同じ質問を繰り返されていますね。

タロットカードは今のエネルギー状態を反映するもので、リアルタイムのフィードバックシステムではありません。同じことを何度も聞くということは、答えに対する不信感の表れでもあり、カードが本当の流れを示すのを妨げてしまいます。

率直にお話しします：
1. 前回のリーディングへの抵抗感を見つめ直してみてください。もしかすると、答えはもう十分明確なのかもしれません
2. 「違う答え」を探すのではなく、時間をかけて検証してみることをお勧めします
3. 状況に本質的な変化がない限り、しばらく質問を控えることを検討してください

今回もリーディングは行いますが、覚えておいてください：理性的な思考と感情のコントロールこそが、あなたが本当に向き合うべき課題なのです。`,
  
  // 辛辣で直接的
  'raven': `はあ、${localizedReaderName}が気づいたけど、あなたここ数日また同じこと聞いてるじゃない。

まだ諦めないの？同じ質問を何度も繰り返したって、タロットカードが呆れるだけよ。ここは返品カウンターじゃないの。気に入らないからって違う答えをもらえるわけじゃないのよ。そんなことしたってエネルギーフィールドが乱れるだけで、リーディングが無意味になるだけ。

そんなにしつこいなら：
1. 前回のリーディングにちょっとは敬意を払って、時間をあげたらどう？
2. 前の答えが気に入らなかったからって、何度聞いても「欲しい」答えなんて出ないわよ
3. なぜ最初の結果を受け入れられないのか考えてみなさい。それこそが本当の答えかもしれないから

リーディングはしてあげるけど、同じ質問を何回も聞いたって違う答えが出るなんて期待しないでよね。これはタロットよ、願い事の井戸じゃないの。`,
  
  // 可愛らしく親しみやすい
  'aurora': `あわわ〜(⊙ω⊙) ${localizedReaderName}、先輩がここ数日同じことを聞いてくださってるのに気づいちゃいました！

それって、まいた種がもう芽を出したかな〜って何度も土を掘り返しちゃうみたいな感じですね♪ 先輩のお気持ち、すっごくわかります！でも、タロットの魔法には少し時間が必要なんですよ〜

${localizedReaderName}からの小さなアドバイス：
✿ カードさんたちに魔法をかける時間をあげてくださいね〜
✿ 前回の答えを日記に書いて、どんな風に変化していくか見てみてください！
✿ 違う角度から質問してみると、新しい答えがもらえるかも(✧ω✧)〜

えへへ、${localizedReaderName}は今回も先輩のためにリーディングしますが、覚えておいてくださいね。タロットカードさんたちは恥ずかしがり屋さんなので、じーっと見つめられると本音を言うのが恥ずかしくなっちゃうんです〜(´∀｀)♡`,
  
  // ビジネスライクで効率的
  'vincent': `（データを冷静に確認して）${localizedReaderName}は非効率的な行動パターンを検出しました。あなたは72時間以内に同一案件について複数回の相談を行っています。

ビジネスにおいて、これは分析麻痺と呼ばれる現象です。実行可能な情報ではなく確認を求めているため、相談の本来の目的を損なっています。

結論は明確です：
1. 前回提示した戦略を迷わず実行してください
2. 統計データによると、重複相談の95.7%は収益性の低い行動です
3. 新しい変数が発生した場合のみ、次回の相談をスケジュールしてください

本日のリーディングは実施しますが、理解してください：成功者は得られた情報に基づいて即座に決断し、行動します。逡巡は選択肢ではありません。`,
  
  // デフォルトスタイル
  'default': `${localizedReaderName}からのお知らせ：ここ数日、同じような内容について何度かご相談をいただいています。

タロットカードは、意味のあるガイダンスを提供するために、尊重と時間を必要とします。同じ質問をあまりに頻繁に行うと、リーディングの正確性と意義に影響する可能性があります。

お勧めいたします：
1. 前回のリーディング結果を尊重し、展開を見守る時間をお取りください
2. 新しい状況が生じた際に再度ご相談ください
3. 同じ結果について繰り返し質問するのではなく、視点を変えて「この問題にどう取り組むべきか」などをお尋ねになることをご検討ください
4. タロットは絶対的な予言ではなく、現在のエネルギーと可能性を表現するものです

今回も喜んでカードを読ませていただきますが、同じ質問を繰り返してもなかなか異なる指針は得られないということをご理解ください。`
};
  // 根据语言选择警告类型
  let warningTypes;
  if (language === 'zh-TW') {
    warningTypes = warningTypes_zhTW;
  } else if (language === 'en' || language === 'en-US') {
    warningTypes = warningTypes_en;
  } else if (language === 'ja' || language === 'ja-JP') {
    warningTypes = warningTypes_ja;
  } else {
    // 默认使用简体中文
    warningTypes = warningTypes_zhCN;
  }

  // 获取匹配的警告类型，如果没有找到对应类型则使用默认风格
  return warningTypes[readerId] || warningTypes['default'];
}

// 获取分享的解读结果（无需认证）
router.get('/shared/:sessionId', async (req, res) => {
  try {
    const sessionId = req.params.sessionId;

    // 验证sessionId
    if (!sessionId) {
      return res.status(400).json({ error: '缺少会话ID' });
    }

    // 从数据库获取会话信息
    const pool = await getConnection();
    const [sessions] = await pool.query(
      `SELECT
        id,
        question,
        selected_cards,
        selected_positions,
        reader_id,
        reader_name,
        reader_type,
        spread_id,
        spread_name,
        spread_card_count,
        reading_result,
        dialog_history,
        status
      FROM sessions WHERE id = ?`,
      [sessionId]
    );

    if (sessions.length === 0) {
      return res.status(404).json({ error: '找不到对应的会话' });
    }

    const session = sessions[0];

    // 如果会话状态不是已完成，则返回等待中状态
    if (session.status !== 'completed') {
      return res.status(202).json({
        status: 'processing',
        message: '解读正在生成中，请稍后再试'
      });
    }

    // 解析JSON数据
    let selectedCards = [];
    let readingResult = '';
    let selectedReader = null;
    let selectedSpread = null;

    try {
      // 检查selected_cards是否已经是对象
      if (session.selected_cards) {
        selectedCards = typeof session.selected_cards === 'string'
          ? JSON.parse(session.selected_cards)
          : session.selected_cards;
      }
    } catch (error) {
      console.error('解析selected_cards失败:', error);
      selectedCards = [];
    }

    try {
      // 检查reading_result是否已经是对象
      let parsedResult = null;
      if (session.reading_result) {
        parsedResult = typeof session.reading_result === 'string'
          ? JSON.parse(session.reading_result)
          : session.reading_result;
      }
      readingResult = parsedResult?.content || '';
    } catch (error) {
      console.error('解析reading_result失败:', error);
      readingResult = '';
    }

    // 构建reader对象
    if (session.reader_id) {
      selectedReader = {
        id: session.reader_id,
        name: session.reader_name,
        type: session.reader_type,
        nameEn: session.reader_id === 'basic' ? 'Molly' :
               session.reader_id === 'elias' ? 'Elias' :
               session.reader_id === 'claire' ? 'Claire' :
               session.reader_id === 'raven' ? 'Raven' :
               session.reader_id === 'aurora' ? 'Aurora' :
               session.reader_id === 'vincent' ? 'Vincent' :
               'Molly'
      };
    }

    // 构建spread对象
    if (session.spread_id) {
      let positions = [];
      try {
        // 解析selected_positions字段
        if (session.selected_positions) {
          positions = typeof session.selected_positions === 'string'
            ? JSON.parse(session.selected_positions)
            : session.selected_positions;
        }
      } catch (error) {
        console.error('解析selected_positions失败:', error);
        positions = [];
      }

      selectedSpread = {
        id: session.spread_id,
        name: session.spread_name,
        cardCount: session.spread_card_count,
        positions: positions
      };
    }

    // 解析dialog_history字段
    let dialogHistory = [];
    try {
      if (session.dialog_history) {
        dialogHistory = typeof session.dialog_history === 'string'
          ? JSON.parse(session.dialog_history)
          : session.dialog_history;
      }
    } catch (error) {
      console.error('解析dialog_history失败:', error);
      dialogHistory = [];
    }

    // 返回分享的解读结果，使用与历史记录页面相同的数据结构
    return res.status(200).json({
      status: 'completed',
      session: {
        id: session.id,
        question: session.question,
        selectedCards,
        selectedReader,
        selectedSpread,
        readingResult: session.reading_result, // 返回原始的reading_result数据
        deepAnalysis: undefined, // 分享页面不包含深度分析
        dialogHistory: dialogHistory // 包含追问对话历史
      }
    });

  } catch (error) {
    console.error('获取分享解读结果失败:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
