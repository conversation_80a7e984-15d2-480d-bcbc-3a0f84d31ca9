import axiosInstance from '../utils/axios';
import axios from 'axios';
import { getBrowserFingerprint } from '../utils/fingerprint';

export const generateTarotReading = async (
  question: string, 
  signal?: AbortSignal,
  onStreamData?: (content: string, isParagraphComplete?: boolean, isStreamComplete?: boolean) => void
) => {
  try {
    const selectedReader = JSON.parse(localStorage.getItem('selectedReader') || 'null');
    const selectedSpread = JSON.parse(localStorage.getItem('selectedSpread') || 'null');
    const selectedCards = JSON.parse(localStorage.getItem('selectedCards') || '[]');
    const sessionId = localStorage.getItem('sessionId');
    const language = localStorage.getItem('i18nextLng') || 'zh-CN';

    if (!sessionId) {
      throw new Error('No session ID found');
    }

    // 如果提供了流式回调函数，使用流式请求模式
    if (onStreamData) {
      return new Promise(async (resolve, reject) => {
        // 获取token用于决定使用哪个API端点
        const token = localStorage.getItem('token');

        // 创建请求URL - 根据用户登录状态选择不同的API端点
        const baseURL = axiosInstance.defaults.baseURL || '';
        const apiUrl = token ? `${baseURL}/api/reading` : `${baseURL}/api/tarot`;

        console.log('🔍 [DeepSeek] 开始流式请求');
        console.log('🔍 [DeepSeek] API URL:', apiUrl);
        console.log('🔍 [DeepSeek] Base URL:', baseURL);
        console.log('🔍 [DeepSeek] 使用API端点:', token ? '/api/reading (登录用户)' : '/api/tarot (匿名用户)');

        // 创建请求主体
        const requestBody: any = {
          sessionId,
          question,
          selectedReader,
          selectedSpread,
          selectedCards,
          language
        };

        console.log('🔍 [DeepSeek] Token存在:', !!token);
        console.log('🔍 [DeepSeek] Token长度:', token ? token.length : 0);
        console.log('🔍 [DeepSeek] SessionId:', sessionId);

        // 如果没有token，需要先获取指纹信息（匿名用户）
        if (!token) {
          console.log('🔍 [DeepSeek] 未登录用户，准备获取浏览器指纹');
          try {
            const fingerprint = await getBrowserFingerprint();
            requestBody.fingerprint = fingerprint;
            console.log('🔍 [DeepSeek] 浏览器指纹获取成功:', fingerprint);
          } catch (error) {
            console.error('🔍 [DeepSeek] 获取浏览器指纹失败:', error);
            // 即使指纹获取失败，也继续请求，让后端处理
          }
        } else {
          console.log('🔍 [DeepSeek] 已登录用户，使用Token认证');
        }

        // 创建一个XHR对象用于流式请求
        const xhr = new XMLHttpRequest();
        // 扩展xhr对象，添加跟踪处理进度的属性
        const xhrWithTracking = xhr as XMLHttpRequest & {
          lastProcessedLength: number,
          fullText: string,
          paragraphBuffer: string[]
        };

        // 添加全文属性用于段落处理
        xhrWithTracking.fullText = '';
        // 添加lastProcessedLength属性用于追踪已处理的文本长度
        xhrWithTracking.lastProcessedLength = 0;
        xhrWithTracking.paragraphBuffer = [];

        xhrWithTracking.open('POST', apiUrl);
        xhrWithTracking.setRequestHeader('Content-Type', 'application/json');

        // 添加身份验证令牌
        if (token) {
          xhrWithTracking.setRequestHeader('Authorization', `Bearer ${token}`);
          console.log('🔍 [DeepSeek] 已添加Authorization头部');
        } else {
          console.log('🔍 [DeepSeek] 未添加Authorization头部（匿名用户）');
        }

        console.log('🔍 [DeepSeek] 请求体内容:', JSON.stringify(requestBody, null, 2));

        // 设置响应类型为文本
        xhrWithTracking.responseType = 'text';

        let fullContent = '';
        let isFirstResponse = true; // 标记是否是第一次收到响应，用于打印prompt信息
        
        // 处理流式数据
        xhrWithTracking.onprogress = () => {
          // 获取新增的响应文本
          const newText = xhrWithTracking.responseText;
          
          // 保持追踪上次处理的文本长度
          const lastProcessedLength = xhrWithTracking.lastProcessedLength;
          
          // 只处理新的文本部分
          if (newText.length > lastProcessedLength) {
            const textToProcess = newText.substring(lastProcessedLength);
            
            // 将响应按行分割处理
            const lines = textToProcess.split('\n\n');
            
            // 累积处理的文本
            let accumulatedText = '';
          
          // 处理SSE格式的数据
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonData = line.substring(6); // 移除 "data: " 前缀
                const data = JSON.parse(jsonData);

                // 如果是第一次收到响应，打印prompt信息
                if (isFirstResponse && data.prompt) {
                  console.log('🔍 [AI API] 发送给AI的Prompt:');
                  console.log('📝 [AI API] System Prompt:', data.prompt.system || '未提供');
                  console.log('📝 [AI API] User Prompt:', data.prompt.user || '未提供');
                  isFirstResponse = false;
                }

                if (data.done) {
                  // 流式输出完成
                  console.log('🔍 [AI API] 完整的AI响应结果:');
                  console.log('📄 [AI API] 完整内容长度:', xhrWithTracking.fullText.length);
                  console.log('📄 [AI API] 完整内容:', xhrWithTracking.fullText);
                    // 处理可能的最后一个段落
                    if (xhrWithTracking.fullText.trim().length > 0) {
                      // 处理完整文本进行段落分析
                      processParagraphs(xhrWithTracking.fullText);
                      
                      // 确保最后一个段落也被发送
                      const paragraphCandidates = getParagraphCandidates(xhrWithTracking.fullText);
                      if (paragraphCandidates.length > 0) {
                        const lastParagraph = paragraphCandidates[paragraphCandidates.length - 1].trim();
                        if (lastParagraph.length > 0 && !xhrWithTracking.paragraphBuffer.includes(lastParagraph) && onStreamData) {
                          xhrWithTracking.paragraphBuffer.push(lastParagraph);
                          onStreamData(lastParagraph, true, false); // 先发送最后一个段落，但不标记为完成
                        }
                      }
                    }
                  
                  // 检查是否有伦理问题标记
                  if (data.hasEthicalIssue) {
                    // 保存伦理问题标记到localStorage
                    const sessionId = localStorage.getItem('sessionId');
                    if (sessionId) {
                      localStorage.setItem(`${sessionId}_hasEthicalIssue`, 'true');
                    }
                  }
                  
                  // 无论是否有最后一个段落，都发送一个单独的完成信号
                  if (onStreamData) {
                    // 立即发送完成信号，不再延迟
                    onStreamData('', false, true); // 空内容，表示只是一个流处理完成的信号
                  }
                  
                  resolve(fullContent);
                  return;
                } else if (data.error) {
                  // 处理错误
                  reject(new Error(data.error));
                  return;
                } else if (data.content) {
                  // 添加新内容
                    console.log('📝 [AI API] 收到内容片段:', data.content);
                    accumulatedText += data.content;
                    xhrWithTracking.fullText += data.content;

                    // 尝试分割段落
                    processParagraphs(xhrWithTracking.fullText);
                    fullContent = xhrWithTracking.fullText;
                }
              } catch (e) {
                // console.error('Error parsing SSE data:', line, e);
              }
            }
          }
          
            // 发送这一批次新接收的内容给前端作为不完整的段落
            if (accumulatedText.trim().length > 0 && onStreamData) {
              onStreamData(accumulatedText, false, false);
            }
            
            // 更新已处理的文本长度
            xhrWithTracking.lastProcessedLength = newText.length;
          }
        };
        
        // 处理段落的函数
        function processParagraphs(text: string) {
          // 创建一个段落候选者数组
          let paragraphCandidates = getParagraphCandidates(text);
          
          // 过滤并发送已完成的段落
          for (let i = 0; i < paragraphCandidates.length - 1; i++) {
            const paragraph = paragraphCandidates[i].trim();
            if (paragraph.length > 0 && !xhrWithTracking.paragraphBuffer.includes(paragraph) && onStreamData) {
              xhrWithTracking.paragraphBuffer.push(paragraph);
              onStreamData(paragraph, true);
            }
          }
          
          // 最后一个段落可能是未完成的，暂时不发送
          // 如果文本结尾有换行符，则最后一个段落也是完整的
          const lastParagraph = paragraphCandidates[paragraphCandidates.length - 1];
          if (lastParagraph && (text.endsWith('\n\n') || text.endsWith('\n')) && !xhrWithTracking.paragraphBuffer.includes(lastParagraph) && onStreamData) {
            xhrWithTracking.paragraphBuffer.push(lastParagraph);
            onStreamData(lastParagraph, true);
          }
        }
        
        // 提取段落候选者的辅助函数
        function getParagraphCandidates(text: string): string[] {
          // 首先尝试使用双换行符分割
          const doubleNewlineRegex = /\n\n+/g;
          let paragraphCandidates: string[] = [];
          let startIndex = 0;
          let match;
          let hasDoubleNewlines = false;
          
          // 查找所有双换行
          while ((match = doubleNewlineRegex.exec(text)) !== null) {
            hasDoubleNewlines = true;
            const endIndex = match.index + match[0].length;
            const paragraph = text.substring(startIndex, endIndex).trim();
            
            // 将可能的段落添加到候选数组
            if (paragraph.length > 0) {
              paragraphCandidates.push(paragraph);
            }
            
            startIndex = endIndex;
          }
          
          // 最后添加剩余文本
          const remainingText = text.substring(startIndex).trim();
          if (remainingText.length > 0) {
            paragraphCandidates.push(remainingText);
          }
          
          // 如果没有找到双换行符，尝试使用单换行符分割
          if (!hasDoubleNewlines && paragraphCandidates.length <= 1 && text.includes('\n')) {
            // 重置候选数组
            paragraphCandidates = [];
            startIndex = 0;
            
            // 使用单换行符分割
            const singleNewlineRegex = /\n/g;
            
            while ((match = singleNewlineRegex.exec(text)) !== null) {
              const endIndex = match.index + match[0].length;
              const paragraph = text.substring(startIndex, endIndex).trim();
              
              // 将可能的段落添加到候选数组
              if (paragraph.length > 0) {
                paragraphCandidates.push(paragraph);
              }
              
              startIndex = endIndex;
            }
            
            // 最后添加剩余文本
            const remainingText = text.substring(startIndex).trim();
            if (remainingText.length > 0) {
              paragraphCandidates.push(remainingText);
            }
          }
          
          return paragraphCandidates;
        }
        
        // 处理请求完成
        xhrWithTracking.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // 确保处理最后一段内容
            if (xhrWithTracking.fullText.trim().length > 0) {
              // 从全文获取段落候选
              const paragraphCandidates = getParagraphCandidates(xhrWithTracking.fullText);
              
              // 确保最后一个段落被发送 - 但只有在它尚未被处理过的情况下
              if (paragraphCandidates.length > 0) {
                const lastParagraph = paragraphCandidates[paragraphCandidates.length - 1].trim();
                // 只有当这个段落不在已处理列表中时才发送
                if (lastParagraph.length > 0 && !xhrWithTracking.paragraphBuffer.includes(lastParagraph) && onStreamData) {
                  // 添加到已处理列表并发送
                  xhrWithTracking.paragraphBuffer.push(lastParagraph);
                  onStreamData(lastParagraph, true, false); // 先发送最后一个段落，但不标记为完成
                }
              }
            }
            
            // 发送流处理完成信号
            if (onStreamData) {
              // 立即发送完成信号，不再延迟
              onStreamData('', false, true); // 空内容，表示只是一个流处理完成的信号
            }
            
            resolve(fullContent);
          } else {
            reject(new Error(`HTTP error ${xhr.status}: ${xhr.statusText}`));
          }
        };
        
        // 错误处理
        xhrWithTracking.onerror = () => {
          reject(new Error('Network error occurred'));
        };
        
        // 处理请求取消
        xhrWithTracking.onabort = () => {
          resolve('REQUEST_CANCELED');
        };
        
        // 处理请求状态变化
        xhrWithTracking.onreadystatechange = () => {
          console.log('🔍 [DeepSeek] ReadyState变化:', xhrWithTracking.readyState, 'Status:', xhrWithTracking.status);

          if (xhrWithTracking.readyState === 4) {
            console.log('🔍 [DeepSeek] 请求完成，状态码:', xhrWithTracking.status);
            console.log('🔍 [DeepSeek] 响应文本长度:', xhrWithTracking.responseText?.length || 0);

            // 处理401状态码 - 未授权
            if (xhrWithTracking.status === 401) {
              console.error('🔍 [DeepSeek] 401未授权错误');
              console.error('🔍 [DeepSeek] 响应内容:', xhrWithTracking.responseText);
              console.error('🔍 [DeepSeek] 请求头信息:');
              console.error('🔍 [DeepSeek] - Content-Type: application/json');
              console.error('🔍 [DeepSeek] - Authorization:', token ? `Bearer ${token.substring(0, 10)}...` : '未设置');
              reject(new Error(`HTTP error 401: Unauthorized - ${xhrWithTracking.responseText}`));
              return;
            }

            // 处理409状态码 - 表示会话已在处理中
            if (xhrWithTracking.status === 409) {
              console.log('🔍 [DeepSeek] 409会话冲突');
              try {
                const errorResponse = JSON.parse(xhrWithTracking.responseText);
                if (errorResponse.code === 'SESSION_ALREADY_IN_PROGRESS') {
                  console.log('🔍 [DeepSeek] 后端会话锁检测到重复调用，避免重复处理');
                  // 这里返回一个特殊标记，表示会话已经在处理中
                  resolve('SESSION_IN_PROGRESS');
                  return;
                }
              } catch (e) {
                console.error('🔍 [DeepSeek] 解析409响应失败:', e);
                // 解析失败，按一般错误处理
                reject(new Error(`HTTP error ${xhrWithTracking.status}: ${xhrWithTracking.statusText}`));
              }
            }

            // 处理其他错误状态码
            if (xhrWithTracking.status >= 400) {
              console.error('🔍 [DeepSeek] HTTP错误:', xhrWithTracking.status, xhrWithTracking.statusText);
              console.error('🔍 [DeepSeek] 错误响应:', xhrWithTracking.responseText);
              reject(new Error(`HTTP error ${xhrWithTracking.status}: ${xhrWithTracking.statusText} - ${xhrWithTracking.responseText}`));
            }
          }
        };
        
        // 发送请求
        console.log('🔍 [DeepSeek] 准备发送请求...');
        console.log('🔍 [DeepSeek] 请求方法: POST');
        console.log('🔍 [DeepSeek] 请求URL:', apiUrl);
        console.log('🔍 [DeepSeek] 请求体大小:', JSON.stringify(requestBody).length, '字符');

        try {
          xhrWithTracking.send(JSON.stringify(requestBody));
          console.log('🔍 [DeepSeek] 请求已发送');
        } catch (error) {
          console.error('🔍 [DeepSeek] 发送请求时出错:', error);
          reject(error);
        }
        
        // 处理信号取消
        if (signal) {
          signal.addEventListener('abort', () => {
            xhrWithTracking.abort();
          });
        }
      });
    } else {
      // 非流式请求，使用原来的方式
      const token = localStorage.getItem('token');
      const apiEndpoint = token ? '/api/reading' : '/api/tarot';

      console.log('🔍 [DeepSeek] 开始非流式请求');
      console.log('🔍 [DeepSeek] 使用API端点:', apiEndpoint, token ? '(登录用户)' : '(匿名用户)');
      console.log('🔍 [DeepSeek] SessionId:', sessionId);
      console.log('🔍 [DeepSeek] 问题长度:', question?.length || 0);

      // 准备请求体，匿名用户需要添加指纹
      const requestBody: any = {
        sessionId,
        question,
        selectedReader,
        selectedSpread,
        selectedCards,
        language
      };

      // 如果是匿名用户，添加指纹信息
      if (!token) {
        try {
          const fingerprint = await getBrowserFingerprint();
          requestBody.fingerprint = fingerprint;
          console.log('🔍 [DeepSeek] 匿名用户指纹已添加:', fingerprint);
        } catch (error) {
          console.error('🔍 [DeepSeek] 获取指纹失败:', error);
          // 继续请求，让后端处理
        }
      }

      try {
        const response = await axiosInstance.post(apiEndpoint, requestBody, { signal });

        console.log('🔍 [DeepSeek] 非流式请求成功，状态码:', response.status);
        console.log('🔍 [AI API] 非流式请求 - 完整响应数据:');
        console.log('📄 [AI API] 响应类型:', typeof response.data);
        console.log('📄 [AI API] 响应内容:', response.data);

        // 如果响应包含prompt信息，打印出来
        if (response.data && typeof response.data === 'object' && response.data.prompt) {
          console.log('📝 [AI API] 发送给AI的Prompt:');
          console.log('📝 [AI API] System Prompt:', response.data.prompt.system || '未提供');
          console.log('📝 [AI API] User Prompt:', response.data.prompt.user || '未提供');
        }

        // 检查response.data的结构
        if (response.data && typeof response.data === 'string') {
          // 如果response.data是字符串，直接尝试解析
          const readingData = JSON.parse(response.data);
          return JSON.stringify(readingData);
        } else if (response.data && response.data.reading) {
          // 如果response.data包含reading字段
          return response.data.reading;
        } else if (response.data && response.data.choices && response.data.choices[0] && response.data.choices[0].message) {
          // 如果是OpenAI格式的响应
          const readingData = JSON.parse(response.data.choices[0].message.content);
          return JSON.stringify(readingData);
        } else {
          // 如果都不是，尝试直接返回response.data
          return JSON.stringify(response.data);
        }
      } catch (error) {
        if (axios.isCancel(error)) {
          return 'REQUEST_CANCELED';
        }
        // console.error('Error processing response:', error);
        throw error;
      }
    }
  } catch (error: any) {
    // 处理取消请求的错误
    if (error?.name === 'CanceledError' || error?.name === 'AbortError' || axios.isCancel(error)) {
      return 'REQUEST_CANCELED';
    }
    
    // 处理会话已在处理中的错误
    if (error?.response?.status === 409 && error?.response?.data?.code === 'SESSION_ALREADY_IN_PROGRESS') {
      // console.log('后端会话锁检测到重复调用，避免重复处理');
      return 'SESSION_IN_PROGRESS';
    }
    
    // console.error('Error generating tarot reading:', error);
    throw error;
  }
};

export const generateFollowUpReading = async (followUpQuestion: string, signal?: AbortSignal) => {
  try {
    const sessionId = localStorage.getItem('sessionId');
    const language = localStorage.getItem('i18nextLng') || 'zh-CN';

    if (!sessionId) {
      throw new Error('No session ID found');
    }

    // 确保追问文本不超过100个字符
    const truncatedQuestion = followUpQuestion.slice(0, 100);

    // 创建新的请求
    const response = await axiosInstance.post('/api/followup', {
      followUpQuestion: truncatedQuestion,
      sessionId,
      language
    }, { 
      signal,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // 检查response.data的结构
    if (response.data && typeof response.data === 'string') {
      // 如果response.data是字符串，直接返回
      return response.data;
    } else if (response.data && response.data.isEthicalIntervention) {
      // 如果是伦理干预消息，添加特殊标记并返回
      // console.log('伦理干预:', response.data.category || '未知分类');
      // 将伦理干预消息特殊标记，以便前端可以特殊处理（如添加警告图标等）
      return `[ETHICAL_INTERVENTION]${response.data.reading}`;
    } else if (response.data && response.data.reading) {
      // 如果response.data包含reading字段
      return response.data.reading;
    } else if (response.data && response.data.content) {
      // 如果response.data包含content字段
      return response.data.content;
    } else if (response.data && response.data.choices && response.data.choices[0] && response.data.choices[0].message) {
      // 如果是OpenAI格式的响应
      return response.data.choices[0].message.content;
    } else {
      // 如果都不是，尝试直接返回response.data
      return JSON.stringify(response.data);
    }
  } catch (error: any) {
    // 处理取消请求的错误
    if (error?.name === 'CanceledError' || error?.name === 'AbortError' || axios.isCancel(error)) {
      return 'REQUEST_CANCELED';
    }
    
    // 处理权限错误 - 用户已使用过免费追问
    if (error.response && error.response.status === 403) {
      // 返回一个特殊的错误JSON字符串，前端可以解析并显示特定消息
      return JSON.stringify({
        error: true,
        message: error.response.data.error || '您已使用过一次免费追问，该功能仅对VIP用户开放',
        errorType: 'NO_FREE_FOLLOWUP'
      });
    }
    
    // console.error('Error generating follow-up reading:', error);
    throw error;
  }
};

export const generateDeepAnalysis = async (
  initialAnalysis?: string | null, 
  signal?: AbortSignal,
  onStreamData?: (content: string, isParagraphComplete?: boolean) => void
): Promise<string> => {
  try {
    const sessionId = localStorage.getItem('sessionId');
    const language = localStorage.getItem('i18nextLng') || 'zh-CN';

    if (!sessionId) {
      throw new Error('No session ID found');
    }

    // 检查是否使用流式处理
    if (onStreamData) {
      // 使用EventSource进行SSE连接
      return new Promise((resolve, reject) => {
        // 创建请求URL - 获取完整的API URL
        const baseURL = axiosInstance.defaults.baseURL || '';
        const apiUrl = `${baseURL}/api/deep-analysis`;
        
        // 创建请求主体
        const requestBody = {
          sessionId,
          language,
          initialAnalysis
        };
        
        // 创建一个跟踪响应的XHR对象
        const xhr = new XMLHttpRequest();
        // 扩展xhr对象，添加跟踪处理进度的属性
        const xhrWithTracking = xhr as XMLHttpRequest & { 
          lastProcessedLength: number,
          fullText: string,
          paragraphBuffer: string[]
        };
        
        // 添加全文属性用于段落处理
        xhrWithTracking.fullText = '';
        // 添加lastProcessedLength属性用于追踪已处理的文本长度
        xhrWithTracking.lastProcessedLength = 0;
        xhrWithTracking.paragraphBuffer = [];
        
        xhrWithTracking.open('POST', apiUrl);
        xhrWithTracking.setRequestHeader('Content-Type', 'application/json');
        
        // 添加身份验证令牌
        const token = localStorage.getItem('token');
        if (token) {
          xhrWithTracking.setRequestHeader('Authorization', `Bearer ${token}`);
        }
        
        xhrWithTracking.responseType = 'text';
        
        let analysisContent = {
          prologue: '',
          analysis1: '',
          summary: ''
        };
        
        // 添加调试日志
        // console.log('Starting stream request to:', apiUrl);
        
        xhrWithTracking.onprogress = () => {
          // 获取新的响应文本
          const newText = xhrWithTracking.responseText;
          
          // 调试日志
          // console.log('Received stream data, length:', newText.length);
          
          // 保持追踪上次处理的文本长度
          const lastProcessedLength = xhrWithTracking.lastProcessedLength;
          
          // 只处理新的文本部分
          if (newText.length > lastProcessedLength) {
            const textToProcess = newText.substring(lastProcessedLength);
            // console.log('Processing new content, length:', textToProcess.length);
            
            // 将响应按行分割处理
            const lines = textToProcess.split('\n');
            
            // 累积处理的文本
            let accumulatedText = '';
            
            // 处理每一行
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const jsonData = line.substring(6); // 移除 "data: " 前缀
                  
                  if (jsonData === '[DONE]') {
                    // console.log('Stream complete [DONE] received');
                    // 处理可能的最后一个段落
                    if (xhrWithTracking.fullText.trim().length > 0) {
                      // 处理完整文本进行段落分析
                      processParagraphs(xhrWithTracking.fullText);
                      
                      // 在收到[DONE]信号时，确保最后一个段落也被发送
                      const paragraphCandidates = getParagraphCandidates(xhrWithTracking.fullText);
                      if (paragraphCandidates.length > 0) {
                        const lastParagraph = paragraphCandidates[paragraphCandidates.length - 1].trim();
                        if (lastParagraph.length > 0 && !xhrWithTracking.paragraphBuffer.includes(lastParagraph) && onStreamData) {
                          xhrWithTracking.paragraphBuffer.push(lastParagraph);
                          onStreamData(lastParagraph, true);
                          // console.log('Sending final paragraph from [DONE]:', lastParagraph.substring(0, 20) + '...');
                        }
                      }
                    }
                    continue;
                  }
                  
                  const data = JSON.parse(jsonData);
                  
                  if (data.content) {
                    // console.log('Received content chunk:', data.content);
                    
                    // 添加新内容到当前批次的累积文本
                    accumulatedText += data.content;
                    // 同时更新完整文本用于段落处理
                    xhrWithTracking.fullText += data.content;
                    
                    // 尝试分割段落
                    processParagraphs(xhrWithTracking.fullText);
                    
                    // 更新分析内容
                    if (!analysisContent.prologue) {
                      analysisContent.prologue = "让我们从更深层的维度来解读这个牌阵...";
                    }
                    analysisContent.analysis1 = xhrWithTracking.fullText;
                  }
                  
                  if (data.error) {
                    // console.error('Stream error from server:', data.error);
                    reject(new Error(data.error));
                  }
                } catch (e) {
                  // 输出解析错误的行
                  // console.error('Error parsing line:', line, e);
                  // 尝试直接从行提取内容
                  if (line.includes('"content":"')) {
                    try {
                      // 尝试提取可能的内容
                      const contentMatch = /"content":"([^"]+)"/.exec(line);
                      if (contentMatch && contentMatch[1]) {
                        const content = contentMatch[1];
                        accumulatedText += content;
                        xhrWithTracking.fullText += content;
                        processParagraphs(xhrWithTracking.fullText);
                        // console.log('Extracted content from invalid JSON:', content);
                      }
                    } catch (extractError) {
                      // console.error('Failed to extract content from line:', extractError);
                    }
                  }
                }
              }
            }
            
            // 发送这一批次新接收的内容给前端作为不完整的段落
            if (accumulatedText.trim().length > 0 && onStreamData) {
              onStreamData(accumulatedText, false);
            }
            
            // 更新已处理的文本长度
            xhrWithTracking.lastProcessedLength = newText.length;
          }
        };
        
        // 处理段落的函数
        function processParagraphs(text: string) {
          // 创建一个段落候选者数组
          let paragraphCandidates = getParagraphCandidates(text);
          
          // 过滤并发送已完成的段落
          for (let i = 0; i < paragraphCandidates.length - 1; i++) {
            const paragraph = paragraphCandidates[i].trim();
            if (paragraph.length > 0 && !xhrWithTracking.paragraphBuffer.includes(paragraph) && onStreamData) {
              xhrWithTracking.paragraphBuffer.push(paragraph);
              onStreamData(paragraph, true);
            }
          }
          
          // 最后一个段落可能是未完成的，暂时不发送
          // 如果文本结尾有换行符，则最后一个段落也是完整的
          const lastParagraph = paragraphCandidates[paragraphCandidates.length - 1];
          if (lastParagraph && (text.endsWith('\n\n') || text.endsWith('\n')) && !xhrWithTracking.paragraphBuffer.includes(lastParagraph) && onStreamData) {
            xhrWithTracking.paragraphBuffer.push(lastParagraph);
            onStreamData(lastParagraph, true);
          }
        }
        
        // 提取段落候选者的辅助函数
        function getParagraphCandidates(text: string): string[] {
          // 首先尝试使用双换行符分割
          const doubleNewlineRegex = /\n\n+/g;
          let paragraphCandidates: string[] = [];
          let startIndex = 0;
          let match;
          let hasDoubleNewlines = false;
          
          // 查找所有双换行
          while ((match = doubleNewlineRegex.exec(text)) !== null) {
            hasDoubleNewlines = true;
            const endIndex = match.index + match[0].length;
            const paragraph = text.substring(startIndex, endIndex).trim();
            
            // 将可能的段落添加到候选数组
            if (paragraph.length > 0) {
              paragraphCandidates.push(paragraph);
            }
            
            startIndex = endIndex;
          }
          
          // 最后添加剩余文本
          const remainingText = text.substring(startIndex).trim();
          if (remainingText.length > 0) {
            paragraphCandidates.push(remainingText);
          }
          
          // 如果没有找到双换行符，尝试使用单换行符分割
          if (!hasDoubleNewlines && paragraphCandidates.length <= 1 && text.includes('\n')) {
            // 重置候选数组
            paragraphCandidates = [];
            startIndex = 0;
            
            // 使用单换行符分割
            const singleNewlineRegex = /\n/g;
            
            while ((match = singleNewlineRegex.exec(text)) !== null) {
              const endIndex = match.index + match[0].length;
              const paragraph = text.substring(startIndex, endIndex).trim();
              
              // 将可能的段落添加到候选数组
              if (paragraph.length > 0) {
                paragraphCandidates.push(paragraph);
              }
              
              startIndex = endIndex;
            }
            
            // 最后添加剩余文本
            const remainingText = text.substring(startIndex).trim();
            if (remainingText.length > 0) {
              paragraphCandidates.push(remainingText);
            }
          }
          
          return paragraphCandidates;
        }
        
        xhrWithTracking.onload = () => {
          // console.log('XHR completed with status:', xhrWithTracking.status);
          if (xhrWithTracking.status >= 200 && xhrWithTracking.status < 300) {
            // 确保处理最后一段内容
            if (xhrWithTracking.fullText.trim().length > 0) {
              // 从全文获取段落候选
              const paragraphCandidates = getParagraphCandidates(xhrWithTracking.fullText);
              
              // 确保最后一个段落被发送 - 但只有在它尚未被处理过的情况下
              if (paragraphCandidates.length > 0) {
                const lastParagraph = paragraphCandidates[paragraphCandidates.length - 1].trim();
                // 只有当这个段落不在已处理列表中时才发送
                if (lastParagraph.length > 0 && !xhrWithTracking.paragraphBuffer.includes(lastParagraph) && onStreamData) {
                  // 添加到已处理列表并发送
                  xhrWithTracking.paragraphBuffer.push(lastParagraph);
                  onStreamData(lastParagraph, true);
                  // console.log('Sending final paragraph from onload:', lastParagraph.substring(0, 20) + '...');
                } else {
                  // console.log('Final paragraph already processed, skipping:', lastParagraph.substring(0, 20) + '...');
                }
              }
            }
            
            // 构建最终结果
            analysisContent.summary = "以上就是对这个牌阵的深入解读。";
            resolve(JSON.stringify(analysisContent));
          } else {
            // 更详细的错误信息
            const errorDetail = xhrWithTracking.responseText ? 
              `Response: ${xhrWithTracking.responseText.substring(0, 100)}...` : 
              'No response text';
            // console.error(`HTTP error ${xhrWithTracking.status}`, errorDetail);
            reject(new Error(`HTTP error ${xhrWithTracking.status}: ${errorDetail}`));
          }
        };
        
        xhrWithTracking.onerror = () => {
          // console.error('Network error:', e);
          reject(new Error('Network error occurred while fetching deep analysis'));
        };
        
        if (signal) {
          signal.addEventListener('abort', () => {
            xhrWithTracking.abort();
            reject(new Error('REQUEST_CANCELED'));
          });
        }
        
        // 发送请求
        xhrWithTracking.send(JSON.stringify(requestBody));
      });
    }

    // 如果没有提供流处理回调，使用常规请求
    const response = await axiosInstance.post('/api/deep-analysis', 
      { 
        sessionId,
        language,
        initialAnalysis
      },
      { signal }
    );

    // 检查response.data的结构
    if (response.data && typeof response.data === 'string') {
      // 如果response.data是字符串，直接返回
      return cleanMarkdownFormat(response.data);
    } else if (response.data && response.data.analysis) {
      // 如果response.data包含analysis字段
      return cleanMarkdownFormat(response.data.analysis);
    } else if (response.data && response.data.choices && response.data.choices[0] && response.data.choices[0].message) {
      // 如果是OpenAI格式的响应
      return cleanMarkdownFormat(response.data.choices[0].message.content);
    } else {
      return cleanMarkdownFormat(JSON.stringify(response.data));
    }
  } catch (error: any) {
    // 处理取消请求的错误
    if (error?.name === 'CanceledError' || error?.name === 'AbortError' || axios.isCancel(error)) {
      return 'REQUEST_CANCELED';
    }
    
    // 处理权限错误 - 用户已使用过免费深度解析
    if (error.response && error.response.status === 403) {
      // 返回一个特殊的错误JSON字符串，前端可以解析并显示特定消息
      return JSON.stringify({
        error: true,
        message: error.response.data.error || '您已使用过一次免费深度解析，该功能仅对VIP用户开放',
        errorType: 'NO_FREE_ANALYSIS'
      });
    }
    
    // console.error('Error in deep analysis:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Unknown error occurred');
  }
};

// 辅助函数：清理Markdown格式
const cleanMarkdownFormat = (text: string): string => {
  // 如果需要，在这里添加清理Markdown格式的逻辑
  return text;
};

